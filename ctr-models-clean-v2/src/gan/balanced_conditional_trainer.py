#!/usr/bin/env python3
"""
平衡的条件GAN训练器 - 解决训练不平衡问题
主要改进：
1. 重新平衡学习率 (G:D = 6:1)
2. 弱化判别器，防止过度主导
3. 降低CTR一致性权重，防止过拟合
4. 动态平衡调整机制
"""

import torch
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import numpy as np
from typing import Dict, Tuple
import wandb

from .conditional_models import CTRConditionalGenerator, CTRConditionalDiscriminator


class BalancedConditionalGANConfig:
    """平衡的条件GAN配置"""
    
    def __init__(self):
        # 模型参数
        self.noise_dim = 128
        self.embedding_dim = 16
        self.ctr_embedding_dim = 8
        
        # 重新平衡的训练参数
        self.generator_lr = 3e-4        # 提高生成器学习率
        self.discriminator_lr = 5e-5    # 大幅降低判别器学习率 (6:1 ratio)
        self.batch_size = 512
        self.epochs = 50
        
        # 重新调整的损失权重
        self.lambda_adversarial = 1.0
        self.lambda_ctr_consistency = 0.5  # 降低CTR一致性权重，防止过拟合
        self.lambda_feature_matching = 0.2  # 降低特征匹配权重
        
        # 训练动态平衡
        self.d_steps = 1
        self.g_steps = 2                   # 增加生成器训练频率
        
        # 温度控制 - 更保守的衰减
        self.temperature = 2.0             # 提高初始温度
        self.temperature_decay = 0.998     # 更慢的衰减
        self.min_temperature = 1.0         # 提高最小温度
        
        # 正则化
        self.max_grad_norm = 0.5           # 更强的梯度裁剪
        self.label_smoothing = 0.1
        
        # 判别器弱化策略
        self.discriminator_dropout = 0.5   # 增加dropout
        self.discriminator_noise = 0.1     # 添加噪声
        
        # 日志
        self.log_interval = 100
        self.save_interval = 5
        self.eval_interval = 5


class BalancedConditionalGANTrainer:
    """
    平衡的条件GAN训练器 - 解决判别器主导问题
    """
    
    def __init__(self, generator: CTRConditionalGenerator, 
                 discriminator: CTRConditionalDiscriminator, 
                 config: BalancedConditionalGANConfig):
        self.generator = generator
        self.discriminator = discriminator
        self.config = config
        
        # Setup optimizers with balanced learning rates
        self.g_optimizer = optim.Adam(
            self.generator.parameters(), 
            lr=config.generator_lr, 
            betas=(0.5, 0.999)
        )
        self.d_optimizer = optim.Adam(
            self.discriminator.parameters(), 
            lr=config.discriminator_lr, 
            betas=(0.5, 0.999)
        )
        
        # Training state
        self.current_epoch = 0
        self.total_steps = 0
        self.current_temperature = config.temperature
        
        # Balance monitoring
        self.score_gap_history = []
        self.balance_adjustments = 0
        
        # Setup logger
        self.logger = logging.getLogger(__name__)
        
        # Move models to device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.generator.to(self.device)
        self.discriminator.to(self.device)
        
        self.logger.info(f"Balanced Conditional GAN Trainer initialized on {self.device}")
        self.logger.info(f"Learning rate ratio G:D = {config.generator_lr/config.discriminator_lr:.1f}:1")
    
    def sample_ctr_labels(self, batch_size: int, real_ctr_rate: float = None) -> torch.Tensor:
        """Sample CTR labels for training"""
        if real_ctr_rate is None:
            real_ctr_rate = 0.5
        
        ctr_labels = torch.bernoulli(torch.full((batch_size,), real_ctr_rate))
        return ctr_labels.to(self.device)
    
    def train_discriminator(self, real_batch: Tuple) -> Dict[str, float]:
        """弱化的判别器训练 - 防止过度主导"""
        self.d_optimizer.zero_grad()
        
        real_numeric, real_categorical, real_ctr_labels = real_batch
        real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
        real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
        real_ctr_labels = real_ctr_labels.to(self.device)
        
        batch_size = real_ctr_labels.size(0)
        
        # 添加标签噪声防止过拟合
        if np.random.random() < 0.1:
            # 10%概率翻转部分标签
            flip_mask = torch.rand_like(real_ctr_labels) < 0.1
            real_ctr_labels = torch.where(flip_mask, 1 - real_ctr_labels, real_ctr_labels)
        
        # 添加特征噪声弱化判别器
        if real_numeric is not None:
            real_numeric = real_numeric + self.config.discriminator_noise * torch.randn_like(real_numeric)
        
        # 真实数据处理
        real_rf_score, real_ctr_consistency, _ = self.discriminator(
            ctr_labels=real_ctr_labels,
            numeric_data=real_numeric,
            categorical_data=real_categorical
        )
        
        # 假数据处理
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        with torch.no_grad():
            fake_output = self.generator(
                noise=noise, 
                ctr_labels=fake_ctr_labels, 
                temperature=self.current_temperature, 
                hard=True
            )
        
        fake_rf_score, fake_ctr_consistency, _ = self.discriminator(
            ctr_labels=fake_ctr_labels,
            numeric_data=fake_output['numeric'],
            categorical_embeddings=fake_output['categorical_embeddings']
        )
        
        # 修改的损失计算 - 更温和的目标，防止判别器过强
        real_target = torch.ones_like(real_rf_score) * 0.8  # 不要求100%确信真实
        fake_target = torch.zeros_like(fake_rf_score) + 0.2  # 给假数据一些信用
        
        real_adv_loss = F.binary_cross_entropy(real_rf_score, real_target)
        fake_adv_loss = F.binary_cross_entropy(fake_rf_score, fake_target)
        d_adv_loss = (real_adv_loss + fake_adv_loss) / 2
        
        # 温和的CTR一致性损失 - 防止过拟合
        real_ctr_target = torch.ones_like(real_ctr_consistency) * 0.85  # 允许一些不一致
        fake_ctr_target = torch.ones_like(fake_ctr_consistency) * 0.85
        
        real_ctr_loss = F.binary_cross_entropy(real_ctr_consistency, real_ctr_target)
        fake_ctr_loss = F.binary_cross_entropy(fake_ctr_consistency, fake_ctr_target)
        d_ctr_loss = (real_ctr_loss + fake_ctr_loss) / 2
        
        # 总损失
        d_loss = (self.config.lambda_adversarial * d_adv_loss + 
                 self.config.lambda_ctr_consistency * d_ctr_loss)
        
        d_loss.backward()
        
        # 更强的梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.config.max_grad_norm)
        
        self.d_optimizer.step()
        
        # 记录分数差距用于平衡监控
        score_gap = real_rf_score.mean().item() - fake_rf_score.mean().item()
        self.score_gap_history.append(score_gap)
        if len(self.score_gap_history) > 50:  # 保持最近50个记录
            self.score_gap_history.pop(0)
        
        return {
            'd_loss': d_loss.item(),
            'd_adv_loss': d_adv_loss.item(),
            'd_ctr_loss': d_ctr_loss.item(),
            'real_rf_score': real_rf_score.mean().item(),
            'fake_rf_score': fake_rf_score.mean().item(),
            'real_ctr_consistency': real_ctr_consistency.mean().item(),
            'fake_ctr_consistency': fake_ctr_consistency.mean().item(),
            'score_gap': score_gap
        }
    
    def train_generator(self, real_batch: Tuple) -> Dict[str, float]:
        """增强的生成器训练"""
        self.g_optimizer.zero_grad()
        
        _, _, real_ctr_labels = real_batch
        real_ctr_labels = real_ctr_labels.to(self.device)
        batch_size = real_ctr_labels.size(0)
        
        # Sample CTR labels for generation
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        # Generate fake data
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        fake_output = self.generator(
            noise=noise, 
            ctr_labels=fake_ctr_labels, 
            temperature=self.current_temperature, 
            hard=False  # Use soft sampling for gradients
        )
        
        # Pass through discriminator
        fake_rf_score, fake_ctr_consistency, fake_features = self.discriminator(
            ctr_labels=fake_ctr_labels,
            numeric_data=fake_output['numeric'],
            categorical_embeddings=fake_output['categorical_embeddings']
        )
        
        # Generator adversarial loss
        g_adv_loss = F.binary_cross_entropy(fake_rf_score, torch.ones_like(fake_rf_score))
        
        # Generator CTR consistency loss - 温和目标
        g_ctr_loss = F.binary_cross_entropy(fake_ctr_consistency, torch.ones_like(fake_ctr_consistency) * 0.85)
        
        # Feature matching loss (optional)
        g_feature_loss = 0
        if self.config.lambda_feature_matching > 0:
            # Get real features for matching
            with torch.no_grad():
                real_numeric, real_categorical, _ = real_batch
                real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
                real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
                
                _, _, real_features = self.discriminator(
                    ctr_labels=real_ctr_labels,
                    numeric_data=real_numeric,
                    categorical_data=real_categorical
                )
            
            g_feature_loss = F.mse_loss(fake_features, real_features)
        
        # Total generator loss
        g_loss = (self.config.lambda_adversarial * g_adv_loss + 
                 self.config.lambda_ctr_consistency * g_ctr_loss +
                 self.config.lambda_feature_matching * g_feature_loss)
        
        # Backward pass
        g_loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.config.max_grad_norm)
        
        self.g_optimizer.step()
        
        return {
            'g_loss': g_loss.item(),
            'g_adv_loss': g_adv_loss.item(),
            'g_ctr_loss': g_ctr_loss.item(),
            'g_feature_loss': g_feature_loss if isinstance(g_feature_loss, float) else g_feature_loss.item(),
            'fake_rf_score_g': fake_rf_score.mean().item(),
            'fake_ctr_consistency_g': fake_ctr_consistency.mean().item()
        }
    
    def check_training_balance(self):
        """检查训练平衡并动态调整"""
        if len(self.score_gap_history) >= 10:
            recent_gap = np.mean(self.score_gap_history[-10:])
            
            if recent_gap > 0.6:  # 判别器过强
                # 临时降低判别器学习率
                for param_group in self.d_optimizer.param_groups:
                    param_group['lr'] *= 0.9
                
                # 临时提高生成器学习率
                for param_group in self.g_optimizer.param_groups:
                    param_group['lr'] *= 1.05
                    
                # 提高温度增加探索
                self.current_temperature = min(self.current_temperature * 1.1, 2.0)
                
                self.balance_adjustments += 1
                self.logger.info(f"⚖️ 平衡调整 #{self.balance_adjustments}: "
                              f"D_lr={self.d_optimizer.param_groups[0]['lr']:.2e}, "
                              f"G_lr={self.g_optimizer.param_groups[0]['lr']:.2e}, "
                              f"Temp={self.current_temperature:.3f}, Gap={recent_gap:.3f}")
            
            elif recent_gap < 0.2:  # 生成器过强（罕见但可能）
                # 稍微提高判别器学习率
                for param_group in self.d_optimizer.param_groups:
                    param_group['lr'] *= 1.05
                
                self.logger.info(f"⚖️ 反向调整: 生成器过强，提高判别器学习率")
    
    def train_step(self, real_batch: Tuple) -> Dict[str, float]:
        """单步训练 - 平衡版本"""
        
        # 训练判别器
        d_metrics = {}
        for _ in range(self.config.d_steps):
            step_metrics = self.train_discriminator(real_batch)
            for k, v in step_metrics.items():
                d_metrics[k] = d_metrics.get(k, 0) + v / self.config.d_steps
        
        # 训练生成器 (更多步数)
        g_metrics = {}
        for _ in range(self.config.g_steps):
            step_metrics = self.train_generator(real_batch)
            for k, v in step_metrics.items():
                g_metrics[k] = g_metrics.get(k, 0) + v / self.config.g_steps
        
        # 更新温度
        self.current_temperature = max(
            self.config.min_temperature,
            self.current_temperature * self.config.temperature_decay
        )
        
        # 检查平衡
        if self.total_steps % 500 == 0:
            self.check_training_balance()
        
        # 合并指标
        metrics = {**d_metrics, **g_metrics, 'temperature': self.current_temperature}
        
        self.total_steps += 1
        return metrics
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.generator.train()
        self.discriminator.train()
        
        epoch_metrics = {}
        num_batches = 0
        
        for batch_idx, batch in enumerate(dataloader):
            metrics = self.train_step(batch)
            
            # 累积指标
            for k, v in metrics.items():
                epoch_metrics[k] = epoch_metrics.get(k, 0) + v
            num_batches += 1
            
            # 定期日志
            if batch_idx % self.config.log_interval == 0:
                self.logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}: "
                    f"D_loss={metrics['d_loss']:.4f}, G_loss={metrics['g_loss']:.4f}, "
                    f"Gap={metrics.get('score_gap', 0):.3f}, "
                    f"D_CTR={metrics['d_ctr_loss']:.4f}, G_CTR={metrics['g_ctr_loss']:.4f}"
                )
                
                # Log to wandb
                wandb.log({
                    'batch_step': self.total_steps,
                    'epoch': self.current_epoch,
                    'balance_adjustments': self.balance_adjustments,
                    **metrics
                })
        
        # 平均指标
        for k in epoch_metrics:
            epoch_metrics[k] /= num_batches
        
        self.current_epoch += 1
        return epoch_metrics
    
    def save_checkpoint(self, path: str, epoch: int, metrics: Dict[str, float]):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'config': self.config.__dict__,
            'metrics': metrics,
            'current_temperature': self.current_temperature,
            'total_steps': self.total_steps,
            'balance_adjustments': self.balance_adjustments,
            'score_gap_history': self.score_gap_history
        }
        torch.save(checkpoint, path)
        self.logger.info(f"Balanced checkpoint saved to {path}")
    
    def load_checkpoint(self, path: str):
        """加载检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.current_temperature = checkpoint.get('current_temperature', self.config.temperature)
        self.total_steps = checkpoint.get('total_steps', 0)
        self.balance_adjustments = checkpoint.get('balance_adjustments', 0)
        self.score_gap_history = checkpoint.get('score_gap_history', [])
        
        self.logger.info(f"Balanced checkpoint loaded from {path}, resuming from epoch {self.current_epoch}")


def create_balanced_conditional_dataloader(processed_data, processor, config: BalancedConditionalGANConfig):
    """创建平衡的条件GAN数据加载器"""
    from torch.utils.data import DataLoader
    
    # 复用原有的数据加载器创建逻辑
    from .conditional_trainer import create_conditional_dataloader
    return create_conditional_dataloader(processed_data, processor, config)
