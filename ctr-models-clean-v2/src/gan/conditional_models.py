#!/usr/bin/env python3
"""
Conditional GAN Models for CTR Prediction
Solves the asymmetric training problem by making both generator and discriminator
conditional on CTR labels, ensuring symmetric supervision for real and fake data.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Dict, Any
import numpy as np


class CTRConditionalGenerator(nn.Module):
    """
    CTR-Conditional Generator that takes noise + CTR label as input
    and generates features corresponding to the desired CTR outcome.
    
    Key Innovation: Generator learns feature-to-CTR relationships by being
    explicitly conditioned on the target CTR label during training.
    """
    
    def __init__(self, noise_dim: int, numeric_features: List[str], 
                 categorical_features: List[str], vocab_sizes: List[int],
                 embedding_dim: int = 16, ctr_embedding_dim: int = 8):
        super(CTRConditionalGenerator, self).__init__()
        
        self.noise_dim = noise_dim
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        self.ctr_embedding_dim = ctr_embedding_dim
        
        # CTR label embedding - maps binary CTR to dense representation
        self.ctr_embedding = nn.Embedding(2, ctr_embedding_dim)
        
        # Input dimension: noise + CTR embedding
        input_dim = noise_dim + ctr_embedding_dim
        
        # Shared encoder that processes noise + CTR label
        self.shared_encoder = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.LayerNorm(256),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(256, 512),
            nn.LayerNorm(512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(512, 256),
            nn.LayerNorm(256),
            nn.LeakyReLU(0.2)
        )
        
        # Numeric feature generator
        if self.num_numeric > 0:
            self.numeric_generator = nn.Sequential(
                nn.Linear(256, 128),
                nn.LayerNorm(128),
                nn.LeakyReLU(0.2),
                nn.Linear(128, 64),
                nn.LeakyReLU(0.2),
                nn.Linear(64, self.num_numeric),
                nn.Tanh()  # Normalized output
            )
        else:
            self.numeric_generator = None
            
        # Categorical feature generators
        if self.num_categorical > 0:
            self.categorical_generators = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(256, 128),
                    nn.LayerNorm(128),
                    nn.LeakyReLU(0.2),
                    nn.Linear(128, 64),
                    nn.LeakyReLU(0.2),
                    nn.Linear(64, vocab_size)
                ) for vocab_size in vocab_sizes
            ])
            
            # Categorical embeddings for output
            self.categorical_embeddings = nn.ModuleList([
                nn.Embedding(vocab_size, embedding_dim)
                for vocab_size in vocab_sizes
            ])
        else:
            self.categorical_generators = None
            self.categorical_embeddings = None
    
    def forward(self, noise: torch.Tensor, ctr_labels: torch.Tensor, 
                temperature: float = 1.0, hard: bool = False):
        """
        Forward pass with noise and CTR labels as input
        
        Args:
            noise: Random noise tensor [batch_size, noise_dim]
            ctr_labels: CTR labels [batch_size] with values 0 or 1
            temperature: Temperature for Gumbel softmax
            hard: Whether to use hard sampling for categorical features
        """
        batch_size = noise.size(0)
        
        # Embed CTR labels
        ctr_embedded = self.ctr_embedding(ctr_labels.long())  # [batch_size, ctr_embedding_dim]
        
        # Concatenate noise and CTR embedding
        conditional_input = torch.cat([noise, ctr_embedded], dim=-1)
        
        # Shared encoding
        encoded = self.shared_encoder(conditional_input)
        
        # Generate numeric features
        numeric_output = None
        if self.numeric_generator is not None:
            numeric_output = self.numeric_generator(encoded)
        
        # Generate categorical features
        cat_logits = []
        cat_probs = []
        cat_embeddings = []
        
        if self.categorical_generators is not None:
            for i, (cat_gen, cat_emb) in enumerate(zip(self.categorical_generators, 
                                                      self.categorical_embeddings)):
                # Generate logits
                logits = cat_gen(encoded)
                cat_logits.append(logits)
                
                # Apply Gumbel softmax
                probs = F.gumbel_softmax(logits, tau=temperature, hard=hard, dim=-1)
                cat_probs.append(probs)
                
                # Get embeddings
                if hard:
                    indices = torch.argmax(probs, dim=-1)
                    emb = cat_emb(indices)
                else:
                    emb = torch.matmul(probs, cat_emb.weight)
                cat_embeddings.append(emb)
        
        return {
            'numeric': numeric_output,
            'categorical_logits': cat_logits if cat_logits else None,
            'categorical_probs': cat_probs if cat_probs else None,
            'categorical_embeddings': cat_embeddings if cat_embeddings else None
        }


class CTRConditionalDiscriminator(nn.Module):
    """
    CTR-Conditional Discriminator that takes features + CTR label as input
    and learns to distinguish real vs fake feature-CTR pairs.
    
    Key Innovation: Discriminator learns what feature patterns are consistent
    with given CTR labels, providing symmetric supervision for real and fake data.
    """
    
    def __init__(self, numeric_features: List[str], categorical_features: List[str],
                 vocab_sizes: List[int], embedding_dim: int = 16, 
                 ctr_embedding_dim: int = 8):
        super(CTRConditionalDiscriminator, self).__init__()
        
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        self.ctr_embedding_dim = ctr_embedding_dim
        
        # CTR label embedding - same as generator
        self.ctr_embedding = nn.Embedding(2, ctr_embedding_dim)
        
        # Categorical embeddings
        if self.num_categorical > 0:
            self.categorical_embeddings = nn.ModuleList([
                nn.Embedding(vocab_size, embedding_dim)
                for vocab_size in vocab_sizes
            ])
        else:
            self.categorical_embeddings = None
        
        # Calculate input dimension
        feature_dim = self.num_numeric + self.num_categorical * embedding_dim
        input_dim = feature_dim + ctr_embedding_dim
        
        # Feature processor with CTR conditioning
        self.feature_processor = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.LayerNorm(256),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            
            nn.Linear(128, 64),
            nn.LeakyReLU(0.2)
        )
        
        # Real/Fake classification head
        self.real_fake_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.LeakyReLU(0.2),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
        
        # CTR consistency head - predicts if features match the given CTR label
        self.ctr_consistency_head = nn.Sequential(
            nn.Linear(64, 32),
            nn.LeakyReLU(0.2),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )
    
    def forward(self, ctr_labels: torch.Tensor, numeric_data: Optional[torch.Tensor] = None,
                categorical_data: Optional[torch.Tensor] = None, 
                categorical_embeddings: Optional[List[torch.Tensor]] = None):
        """
        Forward pass with features and CTR labels as input
        
        Args:
            ctr_labels: CTR labels [batch_size] with values 0 or 1
            numeric_data: Numeric features [batch_size, num_numeric]
            categorical_data: Categorical indices [batch_size, num_categorical]
            categorical_embeddings: Pre-computed categorical embeddings
        """
        batch_size = ctr_labels.size(0)
        features = []
        
        # Embed CTR labels
        ctr_embedded = self.ctr_embedding(ctr_labels.long())
        
        # Process numeric features
        if numeric_data is not None and self.num_numeric > 0:
            features.append(numeric_data)
        elif self.num_numeric > 0:
            features.append(torch.zeros(batch_size, self.num_numeric, device=self.get_device()))
        
        # Process categorical features
        if self.num_categorical > 0:
            if categorical_embeddings is not None:
                # Use pre-computed embeddings (from generator)
                for emb in categorical_embeddings:
                    if emb is not None:
                        features.append(emb)
            elif categorical_data is not None:
                # Compute embeddings from indices
                for i, embedding_layer in enumerate(self.categorical_embeddings):
                    indices = categorical_data[:, i]
                    emb = embedding_layer(indices)
                    features.append(emb)
            else:
                # Zero embeddings as fallback
                for embedding_layer in self.categorical_embeddings:
                    zero_emb = torch.zeros(batch_size, self.embedding_dim, device=self.get_device())
                    features.append(zero_emb)
        
        # Concatenate all features
        if features:
            feature_vector = torch.cat(features, dim=-1)
        else:
            raise ValueError("No input features provided")
        
        # Concatenate features with CTR embedding
        conditional_input = torch.cat([feature_vector, ctr_embedded], dim=-1)
        
        # Process through network
        processed = self.feature_processor(conditional_input)
        
        # Get predictions
        real_fake_score = self.real_fake_head(processed)
        ctr_consistency_score = self.ctr_consistency_head(processed)
        
        return real_fake_score, ctr_consistency_score, processed
    
    def get_device(self):
        """Get model device"""
        return next(self.parameters()).device


def test_conditional_models():
    """Test the conditional GAN models"""
    # Test parameters
    batch_size = 16
    noise_dim = 128
    numeric_features = ['I1', 'I2', 'I3']
    categorical_features = ['C1', 'C2']
    vocab_sizes = [100, 50]
    
    print("Testing Conditional GAN Models...")
    
    # Create models
    generator = CTRConditionalGenerator(
        noise_dim=noise_dim,
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes
    )
    
    discriminator = CTRConditionalDiscriminator(
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes
    )
    
    # Test data
    noise = torch.randn(batch_size, noise_dim)
    ctr_labels = torch.randint(0, 2, (batch_size,))
    
    print(f"Input shapes - Noise: {noise.shape}, CTR labels: {ctr_labels.shape}")
    
    # Test generator
    gen_output = generator(noise, ctr_labels, temperature=1.0, hard=True)
    print("Generator output shapes:")
    if gen_output['numeric'] is not None:
        print(f"  Numeric: {gen_output['numeric'].shape}")
    if gen_output['categorical_embeddings'] is not None:
        print(f"  Categorical embeddings: {len(gen_output['categorical_embeddings'])} features")
        for i, emb in enumerate(gen_output['categorical_embeddings']):
            print(f"    Feature {i}: {emb.shape}")
    
    # Test discriminator
    disc_output = discriminator(
        ctr_labels=ctr_labels,
        numeric_data=gen_output['numeric'],
        categorical_embeddings=gen_output['categorical_embeddings']
    )
    
    print("Discriminator output shapes:")
    print(f"  Real/Fake score: {disc_output[0].shape}")
    print(f"  CTR consistency score: {disc_output[1].shape}")
    print(f"  Features: {disc_output[2].shape}")
    
    print("✅ Conditional GAN models test passed!")


if __name__ == "__main__":
    test_conditional_models()
