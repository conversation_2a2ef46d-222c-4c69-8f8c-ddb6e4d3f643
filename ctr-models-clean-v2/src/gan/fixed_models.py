#!/usr/bin/env python3
"""
修复的GAN模型 - 解决维度不匹配和训练不稳定问题
主要修复：
1. 统一真实数据和生成数据的维度处理
2. 修复判别器的NaN输出问题
3. 改进特征处理和嵌入逻辑
4. 确保所有输出都在正确的范围内
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Tuple, Optional
import numpy as np


class DimensionFixedGenerator(nn.Module):
    """
    维度修复的生成器 - 确保输出与真实数据维度一致
    """
    
    def __init__(self, noise_dim: int, numeric_features: List[str], 
                 categorical_features: List[str], vocab_sizes: List[int],
                 embedding_dim: int = 16):
        super(DimensionFixedGenerator, self).__init__()
        
        self.noise_dim = noise_dim
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        self.vocab_sizes = vocab_sizes
        
        # 噪声编码器 - 增强稳定性
        self.noise_encoder = nn.Sequential(
            nn.Linear(noise_dim, 512),
            nn.BatchNorm1d(512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(512, 512),
            nn.BatchNorm1d(512),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2)
        )
        
        # 数值特征生成器 - 改进架构
        if self.num_numeric > 0:
            self.numeric_generator = nn.Sequential(
                nn.Linear(256, 128),
                nn.BatchNorm1d(128),
                nn.LeakyReLU(0.2),
                nn.Dropout(0.2),
                
                nn.Linear(128, 64),
                nn.BatchNorm1d(64),
                nn.LeakyReLU(0.2),
                
                nn.Linear(64, self.num_numeric)
                # 移除Tanh，允许更大范围的输出
            )
        else:
            self.numeric_generator = None
            
        # 类别特征生成器 - 每个特征独立处理
        if self.num_categorical > 0:
            self.categorical_generators = nn.ModuleList()
            self.categorical_embeddings = nn.ModuleList()
            
            # 计算每个类别特征的噪声维度
            cat_noise_dim = 256 // max(self.num_categorical, 1)
            
            for i, vocab_size in enumerate(vocab_sizes):
                # 每个类别特征的生成器
                generator = nn.Sequential(
                    nn.Linear(cat_noise_dim, 64),
                    nn.BatchNorm1d(64),
                    nn.LeakyReLU(0.2),
                    nn.Dropout(0.1),
                    
                    nn.Linear(64, 32),
                    nn.LeakyReLU(0.2),
                    
                    nn.Linear(32, vocab_size)
                    # 不加Softmax，在forward中处理
                )
                self.categorical_generators.append(generator)
                
                # 对应的embedding层
                embedding = nn.Embedding(vocab_size, embedding_dim)
                # 初始化embedding权重
                nn.init.xavier_uniform_(embedding.weight)
                self.categorical_embeddings.append(embedding)
        else:
            self.categorical_generators = None
            self.categorical_embeddings = None
    
    def forward(self, noise: torch.Tensor, temperature: float = 1.0, hard: bool = False) -> Dict[str, torch.Tensor]:
        batch_size = noise.size(0)
        
        # 编码噪声
        encoded = self.noise_encoder(noise)
        
        # 分配给不同特征类型
        numeric_output = None
        categorical_logits = []
        categorical_probs = []
        categorical_embeddings = []
        
        # 生成数值特征
        if self.numeric_generator is not None:
            numeric_noise = encoded[:, :256]
            numeric_output = self.numeric_generator(numeric_noise)
            # 添加数值稳定性
            numeric_output = torch.clamp(numeric_output, -10, 10)
        
        # 生成类别特征
        if self.categorical_generators is not None:
            categorical_noise = encoded[:, 256:]
            cat_noise_dim = categorical_noise.size(1) // self.num_categorical
            
            for i, (generator, embedding) in enumerate(zip(self.categorical_generators, self.categorical_embeddings)):
                start_idx = i * cat_noise_dim
                end_idx = start_idx + cat_noise_dim
                cat_noise = categorical_noise[:, start_idx:end_idx]
                
                # 生成logits
                logits = generator(cat_noise)
                categorical_logits.append(logits)
                
                # 计算概率 - 添加数值稳定性
                logits_stable = torch.clamp(logits, -10, 10)
                probs = F.softmax(logits_stable / max(temperature, 0.1), dim=-1)
                categorical_probs.append(probs)
                
                # 采样或使用Gumbel-Softmax
                if hard:
                    # 硬采样
                    indices = torch.multinomial(probs + 1e-8, 1).squeeze(-1)
                    # 确保索引在有效范围内
                    indices = torch.clamp(indices, 0, embedding.num_embeddings - 1)
                    emb = embedding(indices)
                else:
                    # Gumbel-Softmax软采样
                    gumbel_probs = F.gumbel_softmax(logits_stable, tau=max(temperature, 0.1), hard=False)
                    # 通过embedding权重计算软嵌入
                    emb = torch.matmul(gumbel_probs, embedding.weight)
                
                categorical_embeddings.append(emb)
        
        return {
            'numeric': numeric_output,
            'categorical_logits': categorical_logits,
            'categorical_probs': categorical_probs,
            'categorical_embeddings': categorical_embeddings
        }


class DimensionFixedDiscriminator(nn.Module):
    """
    维度修复的判别器 - 统一处理真实数据和生成数据
    """
    
    def __init__(self, numeric_features: List[str], categorical_features: List[str],
                 vocab_sizes: List[int], embedding_dim: int = 16, hidden_dim: int = 256):
        super(DimensionFixedDiscriminator, self).__init__()
        
        self.numeric_features = numeric_features
        self.categorical_features = categorical_features
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        self.vocab_sizes = vocab_sizes
        self.hidden_dim = hidden_dim
        
        # 为真实数据创建embedding层
        if self.num_categorical > 0:
            self.real_embeddings = nn.ModuleList([
                nn.Embedding(vocab_size, embedding_dim) 
                for vocab_size in vocab_sizes
            ])
            # 初始化embedding权重
            for embedding in self.real_embeddings:
                nn.init.xavier_uniform_(embedding.weight)
        else:
            self.real_embeddings = None
        
        # 计算总输入维度
        self.total_input_dim = self.num_numeric + self.num_categorical * embedding_dim
        
        # 特征处理器 - 增强稳定性
        self.feature_processor = nn.Sequential(
            nn.Linear(self.total_input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.3),
            
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.BatchNorm1d(hidden_dim // 4),
            nn.LeakyReLU(0.2)
        )
        
        # 真假判别头 - 确保输出稳定
        self.real_fake_head = nn.Sequential(
            nn.Linear(hidden_dim // 4, 32),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            nn.Linear(32, 16),
            nn.LeakyReLU(0.2),
            nn.Linear(16, 1),
            nn.Sigmoid()  # 确保输出在 [0, 1]
        )
        
        # CTR预测头 - 确保输出稳定
        self.ctr_head = nn.Sequential(
            nn.Linear(hidden_dim // 4, 64),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.1),
            nn.Linear(32, 1),
            nn.Sigmoid()  # 确保输出在 [0, 1]
        )

    def forward(self, numeric_data: Optional[torch.Tensor] = None,
                categorical_data: Optional[torch.Tensor] = None,
                categorical_embeddings: Optional[List[torch.Tensor]] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        统一的前向传播，处理真实数据和生成数据

        Args:
            numeric_data: 数值特征 [batch_size, num_numeric]
            categorical_data: 类别特征索引 [batch_size, num_categorical] (真实数据)
            categorical_embeddings: 类别特征嵌入列表 (生成数据)
        """
        # 确定batch_size
        if numeric_data is not None:
            batch_size = numeric_data.size(0)
        elif categorical_embeddings is not None and len(categorical_embeddings) > 0:
            batch_size = categorical_embeddings[0].size(0)
        elif categorical_data is not None:
            batch_size = categorical_data.size(0)
        else:
            raise ValueError("No input data provided")

        features = []

        # 处理数值特征
        if self.num_numeric > 0:
            if numeric_data is not None:
                # 确保数值特征维度正确
                if numeric_data.size(1) != self.num_numeric:
                    raise ValueError(f"Numeric feature dimension mismatch: expected {self.num_numeric}, got {numeric_data.size(1)}")
                features.append(numeric_data)
            else:
                # 如果没有数值特征，填充零
                features.append(torch.zeros(batch_size, self.num_numeric, device=self.get_device()))

        # 处理类别特征
        if self.num_categorical > 0:
            if categorical_embeddings is not None:
                # 生成数据：直接使用提供的嵌入
                if len(categorical_embeddings) != self.num_categorical:
                    raise ValueError(f"Categorical embedding count mismatch: expected {self.num_categorical}, got {len(categorical_embeddings)}")

                # 验证每个嵌入的维度
                for i, emb in enumerate(categorical_embeddings):
                    if emb.size(-1) != self.embedding_dim:
                        raise ValueError(f"Categorical embedding {i} dimension mismatch: expected {self.embedding_dim}, got {emb.size(-1)}")

                cat_features = torch.cat(categorical_embeddings, dim=-1)
            elif categorical_data is not None:
                # 真实数据：通过embedding层转换
                if categorical_data.size(1) != self.num_categorical:
                    raise ValueError(f"Categorical data dimension mismatch: expected {self.num_categorical}, got {categorical_data.size(1)}")

                cat_embeds = []
                for i, embedding in enumerate(self.real_embeddings):
                    indices = categorical_data[:, i].long()
                    # 确保索引在有效范围内
                    indices = torch.clamp(indices, 0, embedding.num_embeddings - 1)
                    emb = embedding(indices)
                    cat_embeds.append(emb)
                cat_features = torch.cat(cat_embeds, dim=-1)
            else:
                # 如果没有类别特征，填充零
                cat_features = torch.zeros(batch_size, self.num_categorical * self.embedding_dim, device=self.get_device())

            features.append(cat_features)

        # 合并所有特征
        if len(features) == 0:
            raise ValueError("No features to process")

        combined_features = torch.cat(features, dim=-1)

        # 确保维度正确
        if combined_features.size(1) != self.total_input_dim:
            raise ValueError(f"Feature dimension mismatch: expected {self.total_input_dim}, got {combined_features.size(1)}")

        # 特征处理
        processed_features = self.feature_processor(combined_features)

        # 多任务预测
        real_fake_score = self.real_fake_head(processed_features)
        ctr_score = self.ctr_head(processed_features)

        # 添加数值稳定性检查 - 防止NaN
        real_fake_score = torch.clamp(real_fake_score, 1e-7, 1 - 1e-7)
        ctr_score = torch.clamp(ctr_score, 1e-7, 1 - 1e-7)

        # 检查是否有NaN值
        if torch.isnan(real_fake_score).any():
            print("WARNING: NaN detected in real_fake_score, replacing with 0.5")
            real_fake_score = torch.where(torch.isnan(real_fake_score), torch.tensor(0.5, device=real_fake_score.device), real_fake_score)

        if torch.isnan(ctr_score).any():
            print("WARNING: NaN detected in ctr_score, replacing with 0.5")
            ctr_score = torch.where(torch.isnan(ctr_score), torch.tensor(0.5, device=ctr_score.device), ctr_score)

        return real_fake_score, ctr_score, processed_features

    def get_device(self):
        """获取模型所在设备"""
        return next(self.parameters()).device


def test_fixed_models():
    """测试修复的模型"""
    print("Testing dimension-fixed GAN models...")

    # 测试参数
    batch_size = 32
    noise_dim = 128
    numeric_features = ['I1', 'I2', 'I3']  # 3个数值特征
    categorical_features = ['C1', 'C2', 'C3', 'C4', 'C5']  # 5个类别特征
    vocab_sizes = [100, 50, 200, 150, 300]
    embedding_dim = 16

    # 创建模型
    generator = DimensionFixedGenerator(
        noise_dim=noise_dim,
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes,
        embedding_dim=embedding_dim
    )

    discriminator = DimensionFixedDiscriminator(
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes,
        embedding_dim=embedding_dim
    )

    print("✅ Models created successfully")

    # 测试生成器
    noise = torch.randn(batch_size, noise_dim)
    gen_output = generator(noise, temperature=1.0, hard=False)

    print(f"✅ Generator test passed")
    print(f"  Numeric output shape: {gen_output['numeric'].shape if gen_output['numeric'] is not None else None}")
    print(f"  Categorical embeddings: {len(gen_output['categorical_embeddings'])} features")
    if gen_output['categorical_embeddings']:
        print(f"  First cat embedding shape: {gen_output['categorical_embeddings'][0].shape}")

    # 测试判别器 - 生成数据
    fake_rf_score, fake_ctr_score, fake_features = discriminator(
        numeric_data=gen_output['numeric'],
        categorical_embeddings=gen_output['categorical_embeddings']
    )

    print(f"✅ Discriminator test with generated data passed")
    print(f"  RF score shape: {fake_rf_score.shape}, range: [{fake_rf_score.min():.4f}, {fake_rf_score.max():.4f}]")
    print(f"  CTR score shape: {fake_ctr_score.shape}, range: [{fake_ctr_score.min():.4f}, {fake_ctr_score.max():.4f}]")

    # 测试判别器 - 真实数据
    real_numeric = torch.randn(batch_size, len(numeric_features))
    real_categorical = torch.randint(0, 10, (batch_size, len(categorical_features)))

    real_rf_score, real_ctr_score, real_features = discriminator(
        numeric_data=real_numeric,
        categorical_data=real_categorical
    )

    print(f"✅ Discriminator test with real data passed")
    print(f"  RF score shape: {real_rf_score.shape}, range: [{real_rf_score.min():.4f}, {real_rf_score.max():.4f}]")
    print(f"  CTR score shape: {real_ctr_score.shape}, range: [{real_ctr_score.min():.4f}, {real_ctr_score.max():.4f}]")

    # 检查特征维度一致性
    if fake_features.shape == real_features.shape:
        print(f"✅ Feature dimensions consistent: {fake_features.shape}")
    else:
        print(f"❌ Feature dimension mismatch: fake={fake_features.shape}, real={real_features.shape}")
        return False

    # 测试binary cross-entropy兼容性
    try:
        # 测试真假判别损失
        real_target = torch.ones_like(real_rf_score)
        fake_target = torch.zeros_like(fake_rf_score)

        real_loss = F.binary_cross_entropy(real_rf_score, real_target)
        fake_loss = F.binary_cross_entropy(fake_rf_score, fake_target)

        print(f"✅ Binary cross-entropy test passed")
        print(f"  Real loss: {real_loss.item():.4f}, Fake loss: {fake_loss.item():.4f}")

        # 测试CTR损失
        ctr_target = torch.randint(0, 2, (batch_size, 1)).float()
        ctr_loss = F.binary_cross_entropy(real_ctr_score, ctr_target)
        print(f"  CTR loss: {ctr_loss.item():.4f}")

    except Exception as e:
        print(f"❌ Binary cross-entropy test failed: {e}")
        return False

    print("\n🎉 All dimension-fixed model tests passed!")
    return True


if __name__ == "__main__":
    test_fixed_models()
