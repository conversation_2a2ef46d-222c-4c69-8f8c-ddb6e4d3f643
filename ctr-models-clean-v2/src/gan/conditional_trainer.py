#!/usr/bin/env python3
"""
Conditional GAN Trainer - Solves Asymmetric Training Problem
Implements symmetric training where both real and fake data receive CTR supervision.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
import numpy as np
from typing import Dict, Any, Optional, Tuple
import wandb

from .conditional_models import CTRConditionalGenerator, CTRConditionalDiscriminator


class ConditionalGANConfig:
    """Configuration for Conditional GAN training"""
    
    def __init__(self):
        # Model parameters
        self.noise_dim = 128
        self.embedding_dim = 16
        self.ctr_embedding_dim = 8
        
        # Training parameters
        self.generator_lr = 2e-4
        self.discriminator_lr = 1e-4
        self.batch_size = 512
        self.epochs = 50
        
        # Loss weights
        self.lambda_adversarial = 1.0
        self.lambda_ctr_consistency = 1.0
        self.lambda_feature_matching = 0.5
        
        # Training dynamics
        self.d_steps = 1
        self.g_steps = 1
        self.temperature = 1.0
        self.temperature_decay = 0.995
        self.min_temperature = 0.5
        
        # Regularization
        self.max_grad_norm = 1.0
        self.label_smoothing = 0.1
        
        # Logging
        self.log_interval = 100
        self.save_interval = 5
        self.eval_interval = 5


class ConditionalGANTrainer:
    """
    Trainer for Conditional GAN that solves the asymmetric training problem
    by ensuring both real and fake data receive CTR supervision.
    """
    
    def __init__(self, generator: CTRConditionalGenerator, 
                 discriminator: CTRConditionalDiscriminator, 
                 config: ConditionalGANConfig):
        self.generator = generator
        self.discriminator = discriminator
        self.config = config
        
        # Setup optimizers
        self.g_optimizer = optim.Adam(
            self.generator.parameters(), 
            lr=config.generator_lr, 
            betas=(0.5, 0.999)
        )
        self.d_optimizer = optim.Adam(
            self.discriminator.parameters(), 
            lr=config.discriminator_lr, 
            betas=(0.5, 0.999)
        )
        
        # Training state
        self.current_epoch = 0
        self.total_steps = 0
        self.current_temperature = config.temperature
        
        # Setup logger
        self.logger = logging.getLogger(__name__)
        
        # Move models to device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.generator.to(self.device)
        self.discriminator.to(self.device)
        
        self.logger.info(f"Conditional GAN Trainer initialized on {self.device}")
    
    def sample_ctr_labels(self, batch_size: int, real_ctr_rate: float = None) -> torch.Tensor:
        """
        Sample CTR labels for training.
        For fake data, we sample labels to match real data distribution.
        """
        if real_ctr_rate is None:
            # Default balanced sampling
            real_ctr_rate = 0.5
        
        # Sample CTR labels with the same distribution as real data
        ctr_labels = torch.bernoulli(torch.full((batch_size,), real_ctr_rate))
        return ctr_labels.to(self.device)
    
    def train_discriminator(self, real_batch: Tuple) -> Dict[str, float]:
        """
        Train discriminator with symmetric CTR supervision for real and fake data
        """
        self.d_optimizer.zero_grad()
        
        real_numeric, real_categorical, real_ctr_labels = real_batch
        real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
        real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
        real_ctr_labels = real_ctr_labels.to(self.device)
        
        batch_size = real_ctr_labels.size(0)
        
        # === REAL DATA PROCESSING ===
        # Real data: features + CTR labels (ground truth)
        real_rf_score, real_ctr_consistency, _ = self.discriminator(
            ctr_labels=real_ctr_labels,
            numeric_data=real_numeric,
            categorical_data=real_categorical
        )
        
        # === FAKE DATA PROCESSING ===
        # Sample CTR labels for fake data generation
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        # Generate fake data conditioned on sampled CTR labels
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        with torch.no_grad():
            fake_output = self.generator(
                noise=noise, 
                ctr_labels=fake_ctr_labels, 
                temperature=self.current_temperature, 
                hard=True
            )
        
        # Fake data: generated features + sampled CTR labels
        fake_rf_score, fake_ctr_consistency, _ = self.discriminator(
            ctr_labels=fake_ctr_labels,
            numeric_data=fake_output['numeric'],
            categorical_embeddings=fake_output['categorical_embeddings']
        )
        
        # === LOSS COMPUTATION ===
        # Adversarial loss (real vs fake classification)
        real_target = torch.ones_like(real_rf_score) * (1.0 - self.config.label_smoothing)
        fake_target = torch.zeros_like(fake_rf_score) + self.config.label_smoothing
        
        real_adv_loss = F.binary_cross_entropy(real_rf_score, real_target)
        fake_adv_loss = F.binary_cross_entropy(fake_rf_score, fake_target)
        d_adv_loss = (real_adv_loss + fake_adv_loss) / 2
        
        # CTR consistency loss (symmetric for real and fake)
        # Real data: features should be consistent with their true CTR labels
        # Use 0.9 instead of 1.0 to prevent overconfidence and maintain diversity
        real_ctr_target = torch.ones_like(real_ctr_consistency) * 0.9  # Target 90% consistency
        real_ctr_loss = F.binary_cross_entropy(real_ctr_consistency, real_ctr_target)

        # Fake data: generated features should be consistent with their conditioning CTR labels
        fake_ctr_target = torch.ones_like(fake_ctr_consistency) * 0.9  # Target 90% consistency
        fake_ctr_loss = F.binary_cross_entropy(fake_ctr_consistency, fake_ctr_target)
        
        d_ctr_loss = (real_ctr_loss + fake_ctr_loss) / 2
        
        # Total discriminator loss
        d_loss = (self.config.lambda_adversarial * d_adv_loss + 
                 self.config.lambda_ctr_consistency * d_ctr_loss)
        
        # Backward pass
        d_loss.backward()
        
        # Gradient clipping
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.config.max_grad_norm)
        
        self.d_optimizer.step()
        
        return {
            'd_loss': d_loss.item(),
            'd_adv_loss': d_adv_loss.item(),
            'd_ctr_loss': d_ctr_loss.item(),
            'real_rf_score': real_rf_score.mean().item(),
            'fake_rf_score': fake_rf_score.mean().item(),
            'real_ctr_consistency': real_ctr_consistency.mean().item(),
            'fake_ctr_consistency': fake_ctr_consistency.mean().item()
        }
    
    def train_generator(self, real_batch: Tuple) -> Dict[str, float]:
        """
        Train generator to fool discriminator and maintain CTR consistency
        """
        self.g_optimizer.zero_grad()
        
        _, _, real_ctr_labels = real_batch
        real_ctr_labels = real_ctr_labels.to(self.device)
        batch_size = real_ctr_labels.size(0)
        
        # Sample CTR labels for generation (matching real distribution)
        real_ctr_rate = real_ctr_labels.float().mean().item()
        fake_ctr_labels = self.sample_ctr_labels(batch_size, real_ctr_rate)
        
        # Generate fake data
        noise = torch.randn(batch_size, self.config.noise_dim, device=self.device)
        fake_output = self.generator(
            noise=noise, 
            ctr_labels=fake_ctr_labels, 
            temperature=self.current_temperature, 
            hard=False  # Use soft sampling for gradients
        )
        
        # Pass through discriminator
        fake_rf_score, fake_ctr_consistency, fake_features = self.discriminator(
            ctr_labels=fake_ctr_labels,
            numeric_data=fake_output['numeric'],
            categorical_embeddings=fake_output['categorical_embeddings']
        )
        
        # Generator adversarial loss (fool discriminator)
        g_adv_loss = F.binary_cross_entropy(fake_rf_score, torch.ones_like(fake_rf_score))
        
        # Generator CTR consistency loss (generate features consistent with conditioning)
        # Use 0.9 target to prevent overconfidence and maintain diversity
        g_ctr_loss = F.binary_cross_entropy(fake_ctr_consistency, torch.ones_like(fake_ctr_consistency) * 0.9)
        
        # Feature matching loss (optional)
        g_feature_loss = 0
        if self.config.lambda_feature_matching > 0:
            # Get real features for matching
            with torch.no_grad():
                real_numeric, real_categorical, _ = real_batch
                real_numeric = real_numeric.to(self.device) if real_numeric is not None else None
                real_categorical = real_categorical.to(self.device) if real_categorical is not None else None
                
                _, _, real_features = self.discriminator(
                    ctr_labels=real_ctr_labels,
                    numeric_data=real_numeric,
                    categorical_data=real_categorical
                )
            
            g_feature_loss = F.mse_loss(fake_features, real_features)
        
        # Total generator loss
        g_loss = (self.config.lambda_adversarial * g_adv_loss + 
                 self.config.lambda_ctr_consistency * g_ctr_loss +
                 self.config.lambda_feature_matching * g_feature_loss)
        
        # Backward pass
        g_loss.backward()
        
        # Gradient clipping
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.config.max_grad_norm)
        
        self.g_optimizer.step()
        
        return {
            'g_loss': g_loss.item(),
            'g_adv_loss': g_adv_loss.item(),
            'g_ctr_loss': g_ctr_loss.item(),
            'g_feature_loss': g_feature_loss if isinstance(g_feature_loss, float) else g_feature_loss.item(),
            'fake_rf_score_g': fake_rf_score.mean().item(),
            'fake_ctr_consistency_g': fake_ctr_consistency.mean().item()
        }
    
    def train_step(self, real_batch: Tuple) -> Dict[str, float]:
        """Single training step with symmetric CTR supervision"""
        
        # Train discriminator
        d_metrics = {}
        for _ in range(self.config.d_steps):
            step_metrics = self.train_discriminator(real_batch)
            for k, v in step_metrics.items():
                d_metrics[k] = d_metrics.get(k, 0) + v / self.config.d_steps
        
        # Train generator
        g_metrics = {}
        for _ in range(self.config.g_steps):
            step_metrics = self.train_generator(real_batch)
            for k, v in step_metrics.items():
                g_metrics[k] = g_metrics.get(k, 0) + v / self.config.g_steps
        
        # Update temperature
        self.current_temperature = max(
            self.config.min_temperature,
            self.current_temperature * self.config.temperature_decay
        )
        
        # Combine metrics
        metrics = {**d_metrics, **g_metrics, 'temperature': self.current_temperature}
        
        self.total_steps += 1
        return metrics
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """Train for one epoch"""
        self.generator.train()
        self.discriminator.train()
        
        epoch_metrics = {}
        num_batches = 0
        
        for batch_idx, batch in enumerate(dataloader):
            metrics = self.train_step(batch)
            
            # Accumulate metrics
            for k, v in metrics.items():
                epoch_metrics[k] = epoch_metrics.get(k, 0) + v
            num_batches += 1
            
            # Log periodically
            if batch_idx % self.config.log_interval == 0:
                self.logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}: "
                    f"D_loss={metrics['d_loss']:.4f}, G_loss={metrics['g_loss']:.4f}, "
                    f"D_CTR={metrics['d_ctr_loss']:.4f}, G_CTR={metrics['g_ctr_loss']:.4f}"
                )
                
                # Log to wandb
                wandb.log({
                    'batch_step': self.total_steps,
                    'epoch': self.current_epoch,
                    **metrics
                })
        
        # Average metrics over epoch
        for k in epoch_metrics:
            epoch_metrics[k] /= num_batches
        
        self.current_epoch += 1
        return epoch_metrics
    
    def save_checkpoint(self, path: str, epoch: int, metrics: Dict[str, float]):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'config': self.config.__dict__,
            'metrics': metrics,
            'current_temperature': self.current_temperature,
            'total_steps': self.total_steps
        }
        torch.save(checkpoint, path)
        self.logger.info(f"Checkpoint saved to {path}")
    
    def load_checkpoint(self, path: str):
        """Load training checkpoint"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.current_temperature = checkpoint.get('current_temperature', self.config.temperature)
        self.total_steps = checkpoint.get('total_steps', 0)
        
        self.logger.info(f"Checkpoint loaded from {path}, resuming from epoch {self.current_epoch}")


def create_conditional_dataloader(processed_data, processor, config: ConditionalGANConfig):
    """Create dataloader for conditional GAN training"""
    from torch.utils.data import DataLoader

    # Extract features and labels from DataFrame
    numeric_data = None
    categorical_data = None

    # Get numeric features
    if len(processor.numeric_features) > 0:
        numeric_data = processed_data[processor.numeric_features].values.astype(np.float32)

    # Get categorical features (as indices)
    if len(processor.categorical_features) > 0:
        categorical_cols = [f"{col}_idx" for col in processor.categorical_features]
        categorical_data = processed_data[categorical_cols].values.astype(np.int64)

    # Get labels
    labels = processed_data[processor.label_col].values.astype(np.float32)

    # Convert to tensors
    tensors = []
    if numeric_data is not None:
        tensors.append(torch.from_numpy(numeric_data))
    else:
        tensors.append(None)

    if categorical_data is not None:
        tensors.append(torch.from_numpy(categorical_data))
    else:
        tensors.append(None)

    tensors.append(torch.from_numpy(labels))

    # Create custom dataset that handles None values
    class ConditionalDataset:
        def __init__(self, numeric, categorical, labels):
            self.numeric = numeric
            self.categorical = categorical
            self.labels = labels
            self.length = len(labels)

        def __len__(self):
            return self.length

        def __getitem__(self, idx):
            return (
                self.numeric[idx] if self.numeric is not None else None,
                self.categorical[idx] if self.categorical is not None else None,
                self.labels[idx]
            )

    dataset = ConditionalDataset(tensors[0], tensors[1], tensors[2])

    def collate_fn(batch):
        numeric_batch = None
        categorical_batch = None
        labels_batch = []

        for numeric, categorical, label in batch:
            if numeric is not None:
                if numeric_batch is None:
                    numeric_batch = []
                numeric_batch.append(numeric)
            if categorical is not None:
                if categorical_batch is None:
                    categorical_batch = []
                categorical_batch.append(categorical)
            labels_batch.append(label)

        # Stack batches
        if numeric_batch is not None:
            numeric_batch = torch.stack(numeric_batch)
        if categorical_batch is not None:
            categorical_batch = torch.stack(categorical_batch)
        labels_batch = torch.stack(labels_batch)

        return numeric_batch, categorical_batch, labels_batch

    return DataLoader(
        dataset,
        batch_size=config.batch_size,
        shuffle=True,
        collate_fn=collate_fn,
        drop_last=True
    )
