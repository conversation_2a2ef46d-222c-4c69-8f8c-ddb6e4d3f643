#!/usr/bin/env python3
"""
Simple test to verify discriminator works correctly
"""

import sys
import torch
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

def test_simple_discriminator():
    """Test discriminator in isolation"""
    print("🧪 Simple Discriminator Test")
    print("=" * 40)
    
    from src.gan.models import ImprovedDiscriminator
    
    # Create simple discriminator
    discriminator = ImprovedDiscriminator(
        numeric_features=['I1', 'I2'],
        categorical_features=['C1'],
        vocab_sizes=[10],
        embedding_dim=4,
        hidden_dim=32
    )
    
    print("✅ Discriminator created")
    
    # Create simple test data
    batch_size = 4
    numeric_data = torch.randn(batch_size, 2)
    categorical_data = torch.randint(0, 5, (batch_size, 1))  # Safe indices
    
    print(f"Test data: numeric={numeric_data.shape}, categorical={categorical_data.shape}")
    print(f"Categorical data values: {categorical_data.flatten().tolist()}")
    
    # Test forward pass
    try:
        rf_score, ctr_score, features = discriminator(
            numeric_data=numeric_data,
            categorical_data=categorical_data
        )
        
        print(f"✅ Forward pass successful!")
        print(f"Output shapes: rf={rf_score.shape}, ctr={ctr_score.shape}, features={features.shape}")
        
        # Check values
        print(f"RF scores: {rf_score.flatten().tolist()}")
        print(f"CTR scores: {ctr_score.flatten().tolist()}")
        
        # Check ranges
        rf_min, rf_max = rf_score.min().item(), rf_score.max().item()
        ctr_min, ctr_max = ctr_score.min().item(), ctr_score.max().item()
        
        print(f"RF range: [{rf_min:.6f}, {rf_max:.6f}]")
        print(f"CTR range: [{ctr_min:.6f}, {ctr_max:.6f}]")
        
        # Validate ranges
        rf_valid = (0 <= rf_min <= rf_max <= 1)
        ctr_valid = (0 <= ctr_min <= ctr_max <= 1)
        
        if rf_valid and ctr_valid:
            print("✅ All outputs are properly bounded [0,1]")
            
            # Test BCE loss
            target = torch.ones_like(rf_score) * 0.7
            try:
                loss = torch.nn.functional.binary_cross_entropy(rf_score, target)
                print(f"✅ BCE loss computation successful: {loss.item():.4f}")
                return True
            except Exception as e:
                print(f"❌ BCE loss failed: {e}")
                return False
        else:
            print(f"❌ Invalid ranges! RF valid: {rf_valid}, CTR valid: {ctr_valid}")
            return False
            
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_discriminator_heads():
    """Test discriminator heads directly"""
    print(f"\n🧪 Testing Discriminator Heads Directly")
    print("-" * 40)
    
    # Create simple heads like in the discriminator
    hidden_dim = 32
    
    real_fake_head = torch.nn.Sequential(
        torch.nn.Linear(hidden_dim // 4, 16),
        torch.nn.LeakyReLU(0.2),
        torch.nn.Dropout(0.2),
        torch.nn.Linear(16, 8),
        torch.nn.LeakyReLU(0.2),
        torch.nn.Linear(8, 1),
        torch.nn.Sigmoid()
    )
    
    ctr_head = torch.nn.Sequential(
        torch.nn.Linear(hidden_dim // 4, 16),
        torch.nn.LeakyReLU(0.2),
        torch.nn.Dropout(0.2),
        torch.nn.Linear(16, 8),
        torch.nn.LeakyReLU(0.2),
        torch.nn.Dropout(0.1),
        torch.nn.Linear(8, 1),
        torch.nn.Sigmoid()
    )
    
    print("✅ Heads created with Sigmoid")
    
    # Test with random input
    batch_size = 4
    input_features = torch.randn(batch_size, hidden_dim // 4)
    
    rf_output = real_fake_head(input_features)
    ctr_output = ctr_head(input_features)
    
    print(f"RF output: {rf_output.flatten().tolist()}")
    print(f"CTR output: {ctr_output.flatten().tolist()}")
    
    # Check ranges
    rf_min, rf_max = rf_output.min().item(), rf_output.max().item()
    ctr_min, ctr_max = ctr_output.min().item(), ctr_output.max().item()
    
    print(f"RF range: [{rf_min:.6f}, {rf_max:.6f}]")
    print(f"CTR range: [{ctr_min:.6f}, {ctr_max:.6f}]")
    
    # Validate
    rf_valid = (0 <= rf_min <= rf_max <= 1)
    ctr_valid = (0 <= ctr_min <= ctr_max <= 1)
    
    if rf_valid and ctr_valid:
        print("✅ Direct head test passed!")
        return True
    else:
        print(f"❌ Direct head test failed! RF valid: {rf_valid}, CTR valid: {ctr_valid}")
        return False

def main():
    """Main test"""
    print("🔍 Discriminator Debug Tests")
    print("=" * 50)
    
    # Test 1: Direct heads
    test1 = test_discriminator_heads()
    
    # Test 2: Full discriminator
    test2 = test_simple_discriminator()
    
    print(f"\n📊 Results:")
    print(f"  Direct heads test: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"  Full discriminator test: {'✅ PASSED' if test2 else '❌ FAILED'}")
    
    if test1 and test2:
        print(f"\n🎉 All tests PASSED!")
        print(f"Discriminator architecture is correct.")
        print(f"The issue must be in the training loop or data.")
    else:
        print(f"\n❌ Tests FAILED!")
        print(f"There's an issue with the discriminator architecture.")
    
    return test1 and test2

if __name__ == "__main__":
    success = main()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILURE'}")
    sys.exit(0 if success else 1)
