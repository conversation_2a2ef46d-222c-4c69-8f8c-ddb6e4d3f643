#!/usr/bin/env python3
"""
测试修复的GAN模型和训练器
验证维度一致性和训练稳定性
"""

import sys
import os
import logging
import torch
import torch.nn.functional as F
from pathlib import Path

# 添加路径
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

from src.gan.fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator, test_fixed_models
from src.gan.fixed_trainer import FixedGANTrainer, FixedGANConfig
from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_model_compatibility():
    """测试模型兼容性"""
    logger.info("Testing model compatibility...")
    
    # 测试基础模型功能
    if not test_fixed_models():
        logger.error("Basic model tests failed")
        return False
    
    logger.info("✅ Model compatibility tests passed")
    return True


def test_trainer_with_synthetic_data():
    """使用合成数据测试训练器"""
    logger.info("Testing trainer with synthetic data...")
    
    try:
        # 创建合成数据
        batch_size = 32
        num_numeric = 13  # Criteo数据集的数值特征数量
        num_categorical = 26  # Criteo数据集的类别特征数量
        
        # 模拟Criteo特征
        numeric_features = [f'I{i+1}' for i in range(num_numeric)]
        categorical_features = [f'C{i+1}' for i in range(num_categorical)]
        vocab_sizes = [1000] * num_categorical  # 统一词汇表大小
        
        # 创建模型
        config = FixedGANConfig()
        config.batch_size = batch_size
        
        generator = DimensionFixedGenerator(
            noise_dim=config.noise_dim,
            numeric_features=numeric_features,
            categorical_features=categorical_features,
            vocab_sizes=vocab_sizes,
            embedding_dim=config.embedding_dim
        )
        
        discriminator = DimensionFixedDiscriminator(
            numeric_features=numeric_features,
            categorical_features=categorical_features,
            vocab_sizes=vocab_sizes,
            embedding_dim=config.embedding_dim,
            hidden_dim=config.hidden_dim
        )
        
        # 创建训练器
        device = torch.device('cpu')  # 使用CPU避免CUDA问题
        trainer = FixedGANTrainer(generator, discriminator, config, device)
        
        logger.info("✅ Trainer created successfully")
        
        # 创建合成批次数据
        real_numeric = torch.randn(batch_size, num_numeric)
        real_categorical = torch.randint(0, 100, (batch_size, num_categorical))  # 安全的索引范围
        real_ctr_labels = torch.randint(0, 2, (batch_size,))
        
        real_batch = (real_numeric, real_categorical, real_ctr_labels)
        
        logger.info("✅ Synthetic batch data created")
        
        # 测试单步训练
        logger.info("Testing single training step...")
        metrics = trainer.train_step(real_batch)
        
        logger.info("✅ Single training step completed")
        logger.info(f"Training metrics: {metrics}")
        
        # 检查指标是否合理
        required_metrics = ['d_loss', 'g_loss', 'd_real_rf_loss', 'd_fake_rf_loss', 'g_adv_loss']
        for metric in required_metrics:
            if metric not in metrics:
                logger.error(f"Missing metric: {metric}")
                return False
            
            if not isinstance(metrics[metric], (int, float)) or metrics[metric] == float('inf'):
                logger.error(f"Invalid metric value for {metric}: {metrics[metric]}")
                return False
        
        logger.info("✅ All metrics are valid")
        
        # 测试多步训练
        logger.info("Testing multiple training steps...")
        for step in range(3):
            metrics = trainer.train_step(real_batch)
            logger.info(f"Step {step+1}: d_loss={metrics['d_loss']:.4f}, g_loss={metrics['g_loss']:.4f}")
        
        logger.info("✅ Multiple training steps completed")
        
    except Exception as e:
        logger.error(f"Trainer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    logger.info("✅ Trainer tests passed")
    return True


def test_dimension_consistency():
    """测试维度一致性"""
    logger.info("Testing dimension consistency...")
    
    try:
        # 模拟不同的特征配置
        configs = [
            # (num_numeric, num_categorical, vocab_sizes)
            (13, 26, [1000] * 26),  # Criteo-like
            (5, 10, [100, 200, 300, 150, 250, 400, 500, 600, 700, 800]),  # Mixed vocab sizes
            (0, 5, [50, 100, 150, 200, 250]),  # Only categorical
            (10, 0, []),  # Only numeric
        ]
        
        for i, (num_numeric, num_categorical, vocab_sizes) in enumerate(configs):
            logger.info(f"Testing config {i+1}: {num_numeric} numeric, {num_categorical} categorical")
            
            numeric_features = [f'I{j+1}' for j in range(num_numeric)]
            categorical_features = [f'C{j+1}' for j in range(num_categorical)]
            
            # 创建模型
            generator = DimensionFixedGenerator(
                noise_dim=128,
                numeric_features=numeric_features,
                categorical_features=categorical_features,
                vocab_sizes=vocab_sizes,
                embedding_dim=16
            )
            
            discriminator = DimensionFixedDiscriminator(
                numeric_features=numeric_features,
                categorical_features=categorical_features,
                vocab_sizes=vocab_sizes,
                embedding_dim=16
            )
            
            # 测试生成和判别
            batch_size = 16
            noise = torch.randn(batch_size, 128)
            
            # 生成数据
            gen_output = generator(noise, temperature=1.0, hard=False)
            
            # 判别生成数据
            fake_rf, fake_ctr, fake_feat = discriminator(
                numeric_data=gen_output['numeric'],
                categorical_embeddings=gen_output['categorical_embeddings']
            )
            
            # 判别真实数据
            if num_numeric > 0:
                real_numeric = torch.randn(batch_size, num_numeric)
            else:
                real_numeric = None
                
            if num_categorical > 0:
                real_categorical = torch.randint(0, 50, (batch_size, num_categorical))
            else:
                real_categorical = None
            
            real_rf, real_ctr, real_feat = discriminator(
                numeric_data=real_numeric,
                categorical_data=real_categorical
            )
            
            # 检查维度一致性
            if fake_feat.shape != real_feat.shape:
                logger.error(f"Config {i+1}: Feature dimension mismatch")
                return False
            
            logger.info(f"✅ Config {i+1} passed: feature shape {fake_feat.shape}")
        
    except Exception as e:
        logger.error(f"Dimension consistency test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    logger.info("✅ Dimension consistency tests passed")
    return True


def main():
    """主测试函数"""
    logger.info("🚀 Starting fixed GAN tests...")
    
    # 强制使用CPU
    device = torch.device('cpu')
    logger.info(f"Using device: {device}")
    
    # 测试1: 模型兼容性
    if not test_model_compatibility():
        logger.error("❌ Model compatibility tests failed")
        return False
    
    # 测试2: 维度一致性
    if not test_dimension_consistency():
        logger.error("❌ Dimension consistency tests failed")
        return False
    
    # 测试3: 训练器功能
    if not test_trainer_with_synthetic_data():
        logger.error("❌ Trainer tests failed")
        return False
    
    logger.info("🎉 All fixed GAN tests passed!")
    logger.info("The dimension mismatch and NaN output issues have been resolved.")
    logger.info("You can now use the fixed models for training.")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
