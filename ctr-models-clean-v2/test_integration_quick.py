#!/usr/bin/env python3
"""
Quick integration test for the fixed GAN training pipeline
Tests the updated train_gan.py with fixed models
"""

import sys
import os
import logging
import torch
from pathlib import Path

# Add paths
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

# Import the fixed components
from src.gan.fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator
from src.gan.fixed_trainer import FixedGA<PERSON>rainer, FixedGANConfig
from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_training_pipeline():
    """Test the complete training pipeline with fixed models"""
    logger.info("🚀 Testing complete training pipeline with fixed models...")
    
    try:
        # 1. Test data preparation (using synthetic data if real data not available)
        logger.info("Step 1: Testing data preparation...")
        
        # Create synthetic data for testing
        import pandas as pd
        import numpy as np
        
        # Simulate Criteo-like data
        n_samples = 1000
        numeric_features = [f'I{i+1}' for i in range(13)]
        categorical_features = [f'C{i+1}' for i in range(26)]
        
        # Create synthetic DataFrame
        data = {}
        
        # Numeric features
        for feat in numeric_features:
            data[feat] = np.random.randn(n_samples)
        
        # Categorical features  
        for i, feat in enumerate(categorical_features):
            vocab_size = np.random.randint(50, 500)
            data[feat] = np.random.randint(0, vocab_size, n_samples)
        
        # CTR labels
        data['label'] = np.random.randint(0, 2, n_samples)
        
        df = pd.DataFrame(data)
        
        # Save to temporary file
        temp_dir = '/tmp/test_gan_data'
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, 'train.csv')
        df.to_csv(temp_file, index=False)
        
        logger.info(f"✅ Created synthetic data: {df.shape}")
        
        # 2. Test data preprocessing
        logger.info("Step 2: Testing data preprocessing...")
        
        from src.gan.improved_data_prep import ImprovedGANDataProcessor
        
        processor = ImprovedGANDataProcessor(
            dataset_name='Criteo',
            data_dir=temp_dir,
            output_dir='/tmp/test_gan_output',
            max_vocab_size=1000,
            min_freq=1
        )
        
        processed_df = processor.preprocess_for_gan(df, is_training=True)
        logger.info(f"✅ Data preprocessing completed: {processed_df.shape}")
        
        # 3. Test dataloader creation
        logger.info("Step 3: Testing dataloader creation...")
        
        config = FixedGANConfig()
        config.batch_size = 32
        
        dataloader = create_improved_dataloader(processed_df, config)
        logger.info(f"✅ Dataloader created: {len(dataloader)} batches")
        
        # 4. Test model creation
        logger.info("Step 4: Testing model creation...")
        
        feature_info = processor.get_feature_info()
        
        generator = DimensionFixedGenerator(
            noise_dim=config.noise_dim,
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim
        )
        
        discriminator = DimensionFixedDiscriminator(
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            hidden_dim=config.hidden_dim
        )
        
        logger.info("✅ Models created successfully")
        
        # 5. Test trainer creation
        logger.info("Step 5: Testing trainer creation...")
        
        device = torch.device('cpu')  # Use CPU for testing
        trainer = FixedGANTrainer(generator, discriminator, config, device)
        
        logger.info("✅ Trainer created successfully")
        
        # 6. Test training step
        logger.info("Step 6: Testing training step...")
        
        for batch_idx, batch in enumerate(dataloader):
            logger.info(f"Processing batch {batch_idx}")
            
            # This should work without dimension errors
            metrics = trainer.train_step(batch)
            
            logger.info("✅ Training step completed!")
            logger.info(f"Sample metrics: d_loss={metrics['d_loss']:.4f}, g_loss={metrics['g_loss']:.4f}")
            
            # Verify metrics are valid
            for key, value in metrics.items():
                if value == float('inf') or value != value:  # Check for inf or NaN
                    logger.error(f"❌ Invalid metric {key}: {value}")
                    return False
            
            break  # Only test first batch
        
        logger.info("✅ All metrics are valid")
        
        # 7. Test multiple training steps
        logger.info("Step 7: Testing multiple training steps...")
        
        batch = next(iter(dataloader))
        for step in range(3):
            metrics = trainer.train_step(batch)
            logger.info(f"Step {step+1}: d_loss={metrics['d_loss']:.4f}, g_loss={metrics['g_loss']:.4f}")
        
        logger.info("✅ Multiple training steps completed")
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        shutil.rmtree('/tmp/test_gan_output', ignore_errors=True)
        
        logger.info("🎉 Complete training pipeline test PASSED!")
        logger.info("")
        logger.info("✅ INTEGRATION SUCCESS:")
        logger.info("  - Data preprocessing: WORKING")
        logger.info("  - Model creation: WORKING")
        logger.info("  - Trainer initialization: WORKING")
        logger.info("  - Training steps: WORKING")
        logger.info("  - No dimension mismatches: CONFIRMED")
        logger.info("  - No NaN outputs: CONFIRMED")
        logger.info("")
        logger.info("🚀 Ready for full training!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    logger.info("🔧 Testing integration of fixed GAN models...")
    
    if test_training_pipeline():
        logger.info("🎉 Integration test PASSED! You can now run full training.")
        return True
    else:
        logger.error("❌ Integration test FAILED!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
