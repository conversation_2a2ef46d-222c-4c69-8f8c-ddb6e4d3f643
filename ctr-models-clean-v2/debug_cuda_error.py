#!/usr/bin/env python3
"""
Debug script to identify the source of CUDA errors
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader
from src.gan.models import ImprovedConditionalGenerator, ImprovedDiscriminator
from src.gan.improved_trainer import ImprovedGANConfig

def debug_data_loading():
    """Debug data loading and preprocessing"""
    print("🔍 Debugging data loading...")
    
    # Load a small sample of data
    processed_data, processor = prepare_improved_gan_data(
        dataset_name='Criteo',
        data_dir='/data/Criteo_x4',
        output_dir='/tmp/debug_gan',
        train_file='train.csv',
        use_cache=False,
        max_samples=1000,  # Very small sample
        max_vocab_size=1000,
        min_freq=1
    )
    
    print(f"✅ Data loaded successfully: {processed_data.shape}")
    print(f"Data types:\n{processed_data.dtypes}")
    
    # Check for problematic values
    for col in processed_data.columns:
        if col.endswith('_idx'):
            min_val = processed_data[col].min()
            max_val = processed_data[col].max()
            print(f"Categorical {col}: range [{min_val}, {max_val}]")
            
            if min_val < 0:
                print(f"  ⚠️  WARNING: Negative values in {col}")
            if max_val > 50000:  # Reasonable upper bound
                print(f"  ⚠️  WARNING: Very large values in {col}")
    
    return processed_data, processor

def debug_dataloader(processed_data, processor):
    """Debug dataloader creation"""
    print("\n🔍 Debugging dataloader...")
    
    config = ImprovedGANConfig()
    config.batch_size = 32  # Small batch size
    
    try:
        dataloader = create_improved_dataloader(processed_data, config)
        print(f"✅ Dataloader created successfully: {len(dataloader)} batches")
        
        # Test loading one batch
        for batch_idx, batch in enumerate(dataloader):
            numeric_data, categorical_data, labels = batch
            
            print(f"Batch {batch_idx}:")
            print(f"  Numeric shape: {numeric_data.shape if numeric_data is not None else None}")
            print(f"  Categorical shape: {categorical_data.shape if categorical_data is not None else None}")
            print(f"  Labels shape: {labels.shape}")
            
            if categorical_data is not None:
                print(f"  Categorical range: [{categorical_data.min()}, {categorical_data.max()}]")
                
                # Check for invalid indices
                if categorical_data.min() < 0:
                    print(f"  ❌ ERROR: Negative categorical indices found!")
                    return False
                    
                if categorical_data.max() > 50000:
                    print(f"  ⚠️  WARNING: Very large categorical indices: {categorical_data.max()}")
            
            if batch_idx >= 2:  # Only check first few batches
                break
                
        return True
        
    except Exception as e:
        print(f"❌ Dataloader creation failed: {e}")
        return False

def debug_model_creation(processor):
    """Debug model creation"""
    print("\n🔍 Debugging model creation...")
    
    feature_info = processor.get_feature_info()
    config = ImprovedGANConfig()
    
    try:
        # Create models
        generator = ImprovedConditionalGenerator(
            noise_dim=config.noise_dim,
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            ctr_embedding_dim=config.ctr_embedding_dim
        )
        
        discriminator = ImprovedDiscriminator(
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            hidden_dim=config.discriminator_hidden_dim
        )
        
        print(f"✅ Models created successfully")
        print(f"Vocab sizes: {feature_info['vocab_sizes']}")
        
        # Check if vocab sizes match the data
        max_vocab = max(feature_info['vocab_sizes']) if feature_info['vocab_sizes'] else 0
        print(f"Max vocab size: {max_vocab}")
        
        return generator, discriminator, True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        return None, None, False

def debug_forward_pass(generator, discriminator, dataloader, device):
    """Debug forward pass"""
    print(f"\n🔍 Debugging forward pass on {device}...")
    
    generator.to(device)
    discriminator.to(device)
    
    try:
        # Get one batch
        batch = next(iter(dataloader))
        numeric_data, categorical_data, labels = batch
        
        # Move to device
        numeric_data = numeric_data.to(device) if numeric_data is not None else None
        categorical_data = categorical_data.to(device) if categorical_data is not None else None
        labels = labels.to(device)
        
        print(f"Data moved to {device}")
        
        # Test discriminator with real data
        print("Testing discriminator with real data...")
        real_rf_score, real_ctr_pred, real_features = discriminator(
            numeric_data=numeric_data,
            categorical_data=categorical_data
        )
        print(f"✅ Real data forward pass successful")
        print(f"  Output shapes: rf={real_rf_score.shape}, ctr={real_ctr_pred.shape}, features={real_features.shape}")
        
        # Test generator
        print("Testing generator...")
        batch_size = labels.size(0)
        noise = torch.randn(batch_size, 128, device=device)
        ctr_labels = torch.randint(0, 2, (batch_size,), device=device)
        
        gen_output = generator(
            noise=noise,
            ctr_labels=ctr_labels,
            temperature=1.0,
            hard=True
        )
        print(f"✅ Generator forward pass successful")
        
        # Test discriminator with generator output
        print("Testing discriminator with generator output...")
        fake_rf_score, fake_ctr_pred, fake_features = discriminator(
            numeric_data=gen_output['numeric'],
            categorical_embeddings=gen_output['categorical_embeddings']
        )
        print(f"✅ Fake data forward pass successful")
        print(f"  Output shapes: rf={fake_rf_score.shape}, ctr={fake_ctr_pred.shape}, features={fake_features.shape}")
        
        # Test feature matching computation
        print("Testing feature matching...")
        if real_features.shape == fake_features.shape:
            feature_diff = torch.mean((real_features.mean(0) - fake_features.mean(0)) ** 2)
            print(f"✅ Feature matching computation successful: {feature_diff.item()}")
        else:
            print(f"⚠️  Feature shape mismatch: real={real_features.shape}, fake={fake_features.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🐛 CUDA Error Debug Script")
    print("=" * 50)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if device.type == 'cuda':
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    try:
        # Step 1: Debug data loading
        processed_data, processor = debug_data_loading()
        
        # Step 2: Debug dataloader
        if not debug_dataloader(processed_data, processor):
            return False
        
        # Step 3: Debug model creation
        generator, discriminator, success = debug_model_creation(processor)
        if not success:
            return False
        
        # Step 4: Create dataloader for testing
        config = ImprovedGANConfig()
        config.batch_size = 16  # Very small batch
        dataloader = create_improved_dataloader(processed_data, config)
        
        # Step 5: Debug forward pass
        if not debug_forward_pass(generator, discriminator, dataloader, device):
            return False
        
        print("\n🎉 All debug tests passed!")
        print("The CUDA error might be related to:")
        print("1. Larger batch sizes")
        print("2. Specific data patterns in larger datasets")
        print("3. Memory issues with full training")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
