#!/usr/bin/env python3
"""
Debug script to test discriminator output ranges
"""

import sys
import torch
import logging
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

def test_discriminator_output():
    """Test discriminator output ranges"""
    print("🔍 Testing Discriminator Output Ranges")
    print("=" * 50)
    
    from src.gan.models import ImprovedDiscriminator
    
    # Create a simple discriminator
    numeric_features = ['I1', 'I2', 'I3']
    categorical_features = ['C1', 'C2']
    vocab_sizes = [100, 50]
    
    discriminator = ImprovedDiscriminator(
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes,
        embedding_dim=16,
        hidden_dim=256
    )
    
    print(f"Created discriminator")
    
    # Create test data
    batch_size = 8
    numeric_data = torch.randn(batch_size, 3)
    categorical_data = torch.randint(0, 10, (batch_size, 2))
    
    print(f"Test data shapes: numeric={numeric_data.shape}, categorical={categorical_data.shape}")
    
    # Test forward pass
    try:
        rf_score, ctr_score, features = discriminator(
            numeric_data=numeric_data,
            categorical_data=categorical_data
        )
        
        print(f"✅ Forward pass successful!")
        print(f"Output shapes: rf_score={rf_score.shape}, ctr_score={ctr_score.shape}, features={features.shape}")
        
        # Check output ranges
        rf_min, rf_max = rf_score.min().item(), rf_score.max().item()
        ctr_min, ctr_max = ctr_score.min().item(), ctr_score.max().item()
        
        print(f"RF score range: [{rf_min:.6f}, {rf_max:.6f}]")
        print(f"CTR score range: [{ctr_min:.6f}, {ctr_max:.6f}]")
        
        # Check if ranges are valid
        rf_valid = (rf_min >= 0 and rf_max <= 1)
        ctr_valid = (ctr_min >= 0 and ctr_max <= 1)
        
        if rf_valid and ctr_valid:
            print("✅ All outputs are properly bounded [0,1]")
            return True
        else:
            print("❌ Outputs are NOT properly bounded!")
            if not rf_valid:
                print(f"  RF scores out of bounds: [{rf_min:.6f}, {rf_max:.6f}]")
            if not ctr_valid:
                print(f"  CTR scores out of bounds: [{ctr_min:.6f}, {ctr_max:.6f}]")
            
            # Debug the discriminator architecture
            print(f"\nDiscriminator architecture:")
            print(f"  real_fake_head: {discriminator.real_fake_head}")
            print(f"  ctr_head: {discriminator.ctr_head}")
            
            return False
            
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_discriminator_reinitialization():
    """Test discriminator re-initialization behavior"""
    print(f"\n🔍 Testing Discriminator Re-initialization")
    print("-" * 50)
    
    from src.gan.models import ImprovedDiscriminator
    
    # Create discriminator
    discriminator = ImprovedDiscriminator(
        numeric_features=['I1', 'I2'],
        categorical_features=['C1'],
        vocab_sizes=[100],
        embedding_dim=16,
        hidden_dim=128
    )
    
    # Test with first dimension
    print("Testing with first input dimension...")
    numeric_data1 = torch.randn(4, 2)
    categorical_data1 = torch.randint(0, 10, (4, 1))
    
    rf1, ctr1, feat1 = discriminator(numeric_data=numeric_data1, categorical_data=categorical_data1)
    print(f"First test: rf_range=[{rf1.min():.4f}, {rf1.max():.4f}], ctr_range=[{ctr1.min():.4f}, {ctr1.max():.4f}]")
    
    # Test with different dimension (should trigger re-initialization)
    print("Testing with generator embeddings (different dimension)...")
    numeric_data2 = torch.randn(4, 2)
    categorical_embeddings = [torch.randn(4, 16)]  # Different from hard embeddings
    
    rf2, ctr2, feat2 = discriminator(numeric_data=numeric_data2, categorical_embeddings=categorical_embeddings)
    print(f"Second test: rf_range=[{rf2.min():.4f}, {rf2.max():.4f}], ctr_range=[{ctr2.min():.4f}, {ctr2.max():.4f}]")
    
    # Check if both outputs are valid
    valid1 = (rf1.min() >= 0 and rf1.max() <= 1 and ctr1.min() >= 0 and ctr1.max() <= 1)
    valid2 = (rf2.min() >= 0 and rf2.max() <= 1 and ctr2.min() >= 0 and ctr2.max() <= 1)
    
    if valid1 and valid2:
        print("✅ Both tests have valid output ranges!")
        return True
    else:
        print("❌ Invalid output ranges detected!")
        print(f"  Test 1 valid: {valid1}")
        print(f"  Test 2 valid: {valid2}")
        
        # Show discriminator heads after re-initialization
        print(f"\nDiscriminator heads after re-initialization:")
        print(f"  real_fake_head: {discriminator.real_fake_head}")
        print(f"  ctr_head: {discriminator.ctr_head}")
        
        return False

def main():
    """Main test function"""
    print("🧪 Discriminator Output Range Debug")
    print("=" * 60)
    
    # Test 1: Basic discriminator output
    test1_passed = test_discriminator_output()
    
    # Test 2: Re-initialization behavior
    test2_passed = test_discriminator_reinitialization()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"  Basic output test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  Re-initialization test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 All tests PASSED! Discriminator outputs are properly bounded.")
        print(f"The issue might be elsewhere in the training loop.")
    else:
        print(f"\n❌ Discriminator output issues found!")
        print(f"This explains the 'all elements should be between 0 and 1' error.")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
