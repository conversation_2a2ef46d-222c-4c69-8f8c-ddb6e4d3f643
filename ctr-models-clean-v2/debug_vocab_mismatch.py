#!/usr/bin/env python3
"""
Debug script to identify vocab size vs actual data mismatch
This is the ROOT CAUSE of the CUDA error
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

from src.gan.improved_data_prep import prepare_improved_gan_data

def debug_vocab_mismatch():
    """Debug the vocab size vs actual data mismatch"""
    print("🔍 Debugging Vocab Size vs Actual Data Mismatch")
    print("=" * 60)
    
    # Load processed data
    processed_data, processor = prepare_improved_gan_data(
        dataset_name='Criteo',
        data_dir='/data/Criteo_x4',
        output_dir='/tmp/debug_vocab',
        train_file='train.csv',
        use_cache=False,
        max_samples=10000,  # Small sample for debugging
        max_vocab_size=20000,
        min_freq=5
    )
    
    print(f"Processed data shape: {processed_data.shape}")
    print(f"Columns: {list(processed_data.columns)}")
    
    # Get feature info
    feature_info = processor.get_feature_info()
    vocab_sizes = feature_info['vocab_sizes']
    categorical_features = feature_info['categorical_features']
    
    print(f"\nVocab sizes from processor: {vocab_sizes}")
    print(f"Number of categorical features: {len(categorical_features)}")
    
    # Check actual data ranges
    print(f"\n📊 Checking actual data ranges vs vocab sizes:")
    print("-" * 50)
    
    mismatches = []
    
    for i, (feature, vocab_size) in enumerate(zip(categorical_features, vocab_sizes)):
        idx_col = f"{feature}_idx"
        
        if idx_col in processed_data.columns:
            actual_min = processed_data[idx_col].min()
            actual_max = processed_data[idx_col].max()
            actual_unique = processed_data[idx_col].nunique()
            
            print(f"{i:2d}. {feature:10s} | Vocab: {vocab_size:5d} | "
                  f"Range: [{actual_min:5.0f}, {actual_max:5.0f}] | "
                  f"Unique: {actual_unique:5d}")
            
            # Check for mismatches
            if actual_max >= vocab_size:
                mismatch = {
                    'feature': feature,
                    'vocab_size': vocab_size,
                    'actual_max': actual_max,
                    'overflow': actual_max - vocab_size + 1
                }
                mismatches.append(mismatch)
                print(f"    ❌ MISMATCH! Max index {actual_max} >= vocab size {vocab_size}")
            
            if actual_min < 0:
                print(f"    ❌ NEGATIVE INDICES! Min index {actual_min}")
        else:
            print(f"{i:2d}. {feature:10s} | ❌ MISSING COLUMN: {idx_col}")
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print(f"Total categorical features: {len(categorical_features)}")
    print(f"Features with mismatches: {len(mismatches)}")
    
    if mismatches:
        print(f"\n🚨 CRITICAL ISSUES FOUND:")
        for mismatch in mismatches:
            print(f"  - {mismatch['feature']}: max_index={mismatch['actual_max']} "
                  f">= vocab_size={mismatch['vocab_size']} "
                  f"(overflow: {mismatch['overflow']})")
        
        print(f"\n💡 ROOT CAUSE IDENTIFIED:")
        print(f"The CUDA error occurs because:")
        print(f"1. Discriminator creates embedding layers with vocab_sizes from preprocessing")
        print(f"2. But actual categorical indices in data exceed these vocab_sizes")
        print(f"3. When embedding layer tries to access index >= vocab_size, CUDA throws device assertion")
        
        print(f"\n🔧 SOLUTIONS:")
        print(f"1. ✅ IMPLEMENTED: Add safety margin to embedding vocab sizes")
        print(f"2. ✅ IMPLEMENTED: Clamp indices to valid range in discriminator forward pass")
        print(f"3. Check data preprocessing to ensure consistent encoding")
        
        return False
    else:
        print(f"✅ No vocab size mismatches found!")
        return True

def check_encoder_consistency():
    """Check if encoders are consistent with the data"""
    print(f"\n🔍 Checking Encoder Consistency")
    print("-" * 40)
    
    # Load a small sample and check encoding
    processed_data, processor = prepare_improved_gan_data(
        dataset_name='Criteo',
        data_dir='/data/Criteo_x4',
        output_dir='/tmp/debug_vocab',
        train_file='train.csv',
        use_cache=True,  # Use cache if available
        max_samples=1000,
        max_vocab_size=20000,
        min_freq=5
    )
    
    # Check each categorical feature
    for feature in processor.categorical_features:
        if feature in processor.categorical_encoders:
            encoder = processor.categorical_encoders[feature]
            vocab_size = len(encoder.classes_)
            
            idx_col = f"{feature}_idx"
            if idx_col in processed_data.columns:
                actual_max = processed_data[idx_col].max()
                
                print(f"{feature:10s}: encoder_classes={vocab_size}, actual_max={actual_max}")
                
                if actual_max >= vocab_size:
                    print(f"  ❌ ENCODER INCONSISTENCY!")
                    
                    # Show some problematic values
                    problematic = processed_data[processed_data[idx_col] >= vocab_size][idx_col]
                    print(f"  Problematic indices: {problematic.head().tolist()}")

def main():
    """Main debug function"""
    try:
        # Check vocab mismatches
        vocab_ok = debug_vocab_mismatch()
        
        # Check encoder consistency
        check_encoder_consistency()
        
        if not vocab_ok:
            print(f"\n🎯 NEXT STEPS:")
            print(f"1. The improved discriminator now has safety margins and bounds checking")
            print(f"2. Try training again - CUDA errors should be resolved")
            print(f"3. Monitor for any remaining index out-of-bounds warnings")
        
        return vocab_ok
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
