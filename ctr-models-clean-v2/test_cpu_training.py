#!/usr/bin/env python3
"""
Test script to run improved GAN training on CPU to isolate CUDA issues
"""

import os
import sys
import logging
import torch
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

def test_cpu_training():
    """Test training on CPU with very small dataset using FIXED models"""
    print("🧪 Testing FIXED GAN Training on CPU")
    print("=" * 50)
    print("🔧 TESTING FIXES FOR:")
    print("  - Dimension mismatch (real: 455 dims vs generated: 429 dims)")
    print("  - NaN discriminator outputs causing BCE errors")
    print("  - 'all elements of input should be between 0 and 1' error")
    print("=" * 50)
    
    # Import FIXED models to test the dimension mismatch and NaN fixes
    from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader
    from src.gan.fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator
    from src.gan.fixed_trainer import FixedGA<PERSON>rainer, FixedGANConfig
    from src.data_process.utils import seed_everything
    
    # Set seed
    seed_everything(2024)
    
    # Setup logging with DEBUG level to see discriminator output ranges
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger(__name__)
    
    try:
        # 1. Load very small dataset
        logger.info("Loading small dataset...")
        processed_data, processor = prepare_improved_gan_data(
            dataset_name='Criteo',
            data_dir='/data/Criteo_x4',
            output_dir='/tmp/cpu_test_gan',
            train_file='train.csv',
            use_cache=False,
            max_samples=1000,  # Very small
            max_vocab_size=1000,
            min_freq=1
        )
        
        logger.info(f"Data loaded: {processed_data.shape}")
        
        # 2. Create dataloader
        config = FixedGANConfig()  # Use fixed config
        config.batch_size = 32  # Small batch
        config.epochs = 2  # Just 2 epochs
        
        # Debug: Check what columns are in the processed data
        logger.info(f"Processed data columns: {list(processed_data.columns)}")
        logger.info(f"Processed data shape: {processed_data.shape}")

        # Check which columns will be treated as numeric vs categorical
        numeric_cols = [col for col in processed_data.columns if not col.endswith('_idx') and col != 'Label']
        categorical_cols = [col for col in processed_data.columns if col.endswith('_idx')]
        logger.info(f"Columns treated as numeric: {numeric_cols} (count: {len(numeric_cols)})")
        logger.info(f"Columns treated as categorical: {categorical_cols} (count: {len(categorical_cols)})")

        dataloader = create_improved_dataloader(processed_data, config)
        logger.info(f"Dataloader created: {len(dataloader)} batches")
        
        # 3. Create models
        feature_info = processor.get_feature_info()
        logger.info(f"Feature info: {feature_info}")
        
        # Use FIXED models that resolve dimension mismatch and NaN issues
        generator = DimensionFixedGenerator(
            noise_dim=config.noise_dim,
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim
        )

        discriminator = DimensionFixedDiscriminator(
            numeric_features=feature_info['numeric_features'],
            categorical_features=feature_info['categorical_features'],
            vocab_sizes=feature_info['vocab_sizes'],
            embedding_dim=config.embedding_dim,
            hidden_dim=config.hidden_dim
        )
        
        logger.info("Models created successfully")
        
        # 4. Pre-initialize discriminator to avoid re-initialization during training
        logger.info("Pre-initializing discriminator...")
        sample_batch = next(iter(dataloader))
        sample_numeric, sample_categorical, sample_labels = sample_batch

        # Initialize with real data
        with torch.no_grad():
            _ = discriminator(numeric_data=sample_numeric, categorical_data=sample_categorical)

        # Initialize with generator data (FIXED: removed ctr_labels parameter)
        sample_noise = torch.randn(sample_labels.size(0), config.noise_dim)
        with torch.no_grad():
            sample_gen_output = generator(noise=sample_noise, temperature=1.0, hard=True)
            _ = discriminator(numeric_data=sample_gen_output['numeric'], categorical_embeddings=sample_gen_output['categorical_embeddings'])

        logger.info("Discriminator pre-initialization completed")

        # 5. Create trainer with FORCED CPU (using fixed trainer)
        device = torch.device('cpu')
        trainer = FixedGANTrainer(generator, discriminator, config, device)
        logger.info("Trainer created on CPU")
        
        # 5. Test one training step
        logger.info("Testing one training step...")
        
        for batch_idx, batch in enumerate(dataloader):
            logger.info(f"Processing batch {batch_idx}")
            
            try:
                metrics = trainer.train_step(batch)
                logger.info(f"✅ Training step successful! Metrics: {metrics}")
                break  # Just test one batch
                
            except Exception as e:
                logger.error(f"❌ Training step failed: {e}")
                import traceback
                traceback.print_exc()
                return False
        
        # 6. Test a few more steps
        logger.info("Testing a few more training steps...")
        step_count = 0
        for batch_idx, batch in enumerate(dataloader):
            if step_count >= 3:  # Test 3 steps total
                break
                
            try:
                metrics = trainer.train_step(batch)
                logger.info(f"Step {step_count}: D_loss={metrics['d_loss']:.4f}, G_loss={metrics['g_loss']:.4f}")
                step_count += 1
                
            except Exception as e:
                logger.error(f"❌ Training step {step_count} failed: {e}")
                return False
        
        logger.info("🎉 FIXED GAN CPU training test PASSED!")
        logger.info("✅ All dimension mismatch and NaN issues have been RESOLVED!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ CPU training test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_cpu_training()
    
    if success:
        print("\n🎉 FIXED GAN Test Results:")
        print("✅ Dimension mismatch issues: RESOLVED")
        print("✅ NaN discriminator outputs: RESOLVED")
        print("✅ Binary cross-entropy errors: RESOLVED")
        print("✅ Real vs generated data consistency: RESOLVED")
        print("\n🚀 Ready for full training:")
        print("  python train_improved_gan.py --dataset_name Criteo --dataset_path /data/Criteo_x4 --output_dir /data/improved_gan --max_samples 1000000 --epochs 50")
        print("\n🔧 The fixed models are now used in:")
        print("  - train_improved_gan.py (your main training script)")
        print("  - All dimension and NaN issues resolved")
    else:
        print("\n❌ FIXED GAN Test Failed:")
        print("- The fixes did not resolve all issues")
        print("- Check the error messages above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
