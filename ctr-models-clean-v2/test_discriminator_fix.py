#!/usr/bin/env python3
"""
Test script to verify the discriminator dimension fix
"""

import torch
import sys
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

from src.gan.models import ImprovedDiscriminator

def test_discriminator_dynamic_init():
    """Test that discriminator handles dynamic input dimensions"""
    print("Testing discriminator dynamic initialization...")
    
    # Test parameters
    numeric_features = ['I1', 'I2', 'I3']  # 3 numeric features
    categorical_features = ['C1', 'C2']    # 2 categorical features
    vocab_sizes = [100, 50]                # vocab sizes for categorical features
    embedding_dim = 16
    
    # Create discriminator
    discriminator = ImprovedDiscriminator(
        numeric_features=numeric_features,
        categorical_features=categorical_features,
        vocab_sizes=vocab_sizes,
        embedding_dim=embedding_dim,
        hidden_dim=256
    )
    
    print(f"Created discriminator with:")
    print(f"  Numeric features: {len(numeric_features)}")
    print(f"  Categorical features: {len(categorical_features)}")
    print(f"  Expected embedding dim per cat feature: {embedding_dim}")
    print(f"  Expected total cat dim: {len(categorical_features) * embedding_dim}")
    print(f"  Expected total input dim: {len(numeric_features) + len(categorical_features) * embedding_dim}")
    
    # Test with real data (numeric + categorical indices)
    batch_size = 32
    numeric_data = torch.randn(batch_size, len(numeric_features))
    categorical_data = torch.randint(0, 10, (batch_size, len(categorical_features)))
    
    print(f"\nTesting with real data:")
    print(f"  Numeric data shape: {numeric_data.shape}")
    print(f"  Categorical data shape: {categorical_data.shape}")
    
    try:
        real_rf_score, real_ctr_pred, real_features = discriminator(
            numeric_data=numeric_data,
            categorical_data=categorical_data
        )
        
        print(f"  ✅ Real data forward pass successful!")
        print(f"  Output shapes: rf_score={real_rf_score.shape}, ctr_pred={real_ctr_pred.shape}, features={real_features.shape}")
        
    except Exception as e:
        print(f"  ❌ Real data forward pass failed: {e}")
        return False
    
    # Test with generator embeddings (different dimension)
    print(f"\nTesting with generator embeddings:")
    
    # Simulate generator output embeddings (might have different dimensions)
    cat_embeddings = [
        torch.randn(batch_size, embedding_dim),  # C1 embedding
        torch.randn(batch_size, embedding_dim)   # C2 embedding
    ]
    
    print(f"  Categorical embeddings: {[emb.shape for emb in cat_embeddings]}")
    
    try:
        fake_rf_score, fake_ctr_pred, fake_features = discriminator(
            numeric_data=numeric_data,
            categorical_embeddings=cat_embeddings
        )
        
        print(f"  ✅ Generator embeddings forward pass successful!")
        print(f"  Output shapes: rf_score={fake_rf_score.shape}, ctr_pred={fake_ctr_pred.shape}, features={fake_features.shape}")
        
    except Exception as e:
        print(f"  ❌ Generator embeddings forward pass failed: {e}")
        return False
    
    # Test dimension consistency
    if real_features.shape == fake_features.shape:
        print(f"  ✅ Feature dimensions consistent between real and fake data!")
    else:
        print(f"  ❌ Feature dimension mismatch: real={real_features.shape}, fake={fake_features.shape}")
        return False
    
    print(f"\n🎉 All discriminator tests passed!")
    return True

def test_different_input_sizes():
    """Test discriminator with different input configurations"""
    print("\nTesting different input configurations...")
    
    configs = [
        # (num_numeric, num_categorical, embedding_dim)
        (5, 3, 16),   # Small config
        (13, 26, 16), # Criteo-like config
        (10, 10, 32), # Medium config with larger embeddings
    ]
    
    for i, (num_numeric, num_categorical, emb_dim) in enumerate(configs):
        print(f"\nConfig {i+1}: {num_numeric} numeric, {num_categorical} categorical, {emb_dim} embedding dim")
        
        numeric_features = [f'I{j}' for j in range(num_numeric)]
        categorical_features = [f'C{j}' for j in range(num_categorical)]
        vocab_sizes = [100] * num_categorical
        
        discriminator = ImprovedDiscriminator(
            numeric_features=numeric_features,
            categorical_features=categorical_features,
            vocab_sizes=vocab_sizes,
            embedding_dim=emb_dim,
            hidden_dim=256
        )
        
        batch_size = 16
        numeric_data = torch.randn(batch_size, num_numeric) if num_numeric > 0 else None
        categorical_data = torch.randint(0, 10, (batch_size, num_categorical)) if num_categorical > 0 else None
        
        try:
            rf_score, ctr_pred, features = discriminator(
                numeric_data=numeric_data,
                categorical_data=categorical_data
            )
            print(f"  ✅ Config {i+1} successful! Feature shape: {features.shape}")
            
        except Exception as e:
            print(f"  ❌ Config {i+1} failed: {e}")
            return False
    
    print(f"\n🎉 All configuration tests passed!")
    return True

def main():
    """Run all tests"""
    print("🧪 Testing Discriminator Dynamic Initialization Fix")
    print("=" * 60)
    
    tests = [
        test_discriminator_dynamic_init,
        test_different_input_sizes
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
        print("-" * 40)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! The discriminator dimension fix is working.")
        print("\nThe training should now work without dimension mismatch errors.")
    else:
        print("❌ Some tests FAILED. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
