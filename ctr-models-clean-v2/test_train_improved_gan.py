#!/usr/bin/env python3
"""
Test the updated train_improved_gan.py with fixed models
This simulates your exact command to verify it works
"""

import sys
import os
import logging
import subprocess
import tempfile
import pandas as pd
import numpy as np
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_criteo_data(output_dir, n_samples=1000):
    """Create synthetic Criteo-like data for testing"""
    logger.info(f"Creating synthetic Criteo data with {n_samples} samples...")
    
    # Criteo feature structure
    numeric_features = [f'I{i+1}' for i in range(13)]
    categorical_features = [f'C{i+1}' for i in range(26)]
    
    data = {}
    
    # Numeric features (simulate log-normal distribution like real Criteo)
    for feat in numeric_features:
        data[feat] = np.random.lognormal(0, 1, n_samples)
    
    # Categorical features (simulate realistic vocab sizes)
    vocab_sizes = [
        # Realistic Criteo vocab sizes
        1000, 500, 2000, 1500, 800, 300, 1200, 600, 100, 2500,
        400, 700, 900, 200, 1100, 1300, 150, 1800, 350, 450,
        650, 250, 550, 750, 950, 1600
    ]
    
    for i, feat in enumerate(categorical_features):
        vocab_size = vocab_sizes[i]
        data[feat] = np.random.randint(0, vocab_size, n_samples)
    
    # CTR labels (realistic CTR rate ~3-5%)
    data['label'] = np.random.binomial(1, 0.04, n_samples)
    
    df = pd.DataFrame(data)
    
    # Save to CSV
    os.makedirs(output_dir, exist_ok=True)
    train_file = os.path.join(output_dir, 'train.csv')
    df.to_csv(train_file, index=False)
    
    logger.info(f"✅ Created synthetic Criteo data: {df.shape}")
    logger.info(f"  CTR rate: {df['label'].mean():.4f}")
    logger.info(f"  Saved to: {train_file}")
    
    return train_file


def test_train_improved_gan():
    """Test the train_improved_gan.py script with fixed models"""
    logger.info("🚀 Testing train_improved_gan.py with fixed models...")
    
    try:
        # Create temporary directories
        with tempfile.TemporaryDirectory() as temp_dir:
            data_dir = os.path.join(temp_dir, 'data')
            output_dir = os.path.join(temp_dir, 'output')
            
            # Create test data
            train_file = create_test_criteo_data(data_dir, n_samples=1000)
            
            # Prepare command (simulating your exact command but with test data)
            cmd = [
                'python', 'scripts/train_improved_gan.py',
                '--dataset_name', 'Criteo',
                '--dataset_path', data_dir,
                '--output_dir', output_dir,
                '--max_samples', '1000',  # Small for testing
                '--epochs', '2',  # Just 2 epochs for testing
                '--batch_size', '32',
                '--force_cpu',  # Force CPU to avoid CUDA issues
                '--debug'
            ]
            
            logger.info(f"Running command: {' '.join(cmd)}")
            
            # Run the training script
            result = subprocess.run(
                cmd,
                cwd=Path(__file__).parent,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Check results
            if result.returncode == 0:
                logger.info("✅ train_improved_gan.py completed successfully!")
                logger.info("Output (last 20 lines):")
                output_lines = result.stdout.split('\n')
                for line in output_lines[-20:]:
                    if line.strip():
                        logger.info(f"  {line}")
                
                # Check if model files were created
                model_files = []
                if os.path.exists(output_dir):
                    for root, dirs, files in os.walk(output_dir):
                        for file in files:
                            if file.endswith(('.pth', '.json')):
                                model_files.append(os.path.join(root, file))
                
                if model_files:
                    logger.info(f"✅ Model files created: {len(model_files)} files")
                    for file in model_files[:5]:  # Show first 5
                        logger.info(f"  {file}")
                else:
                    logger.warning("⚠️ No model files found, but training completed")
                
                return True
                
            else:
                logger.error("❌ train_improved_gan.py failed!")
                logger.error(f"Return code: {result.returncode}")
                logger.error("STDOUT:")
                for line in result.stdout.split('\n')[-20:]:
                    if line.strip():
                        logger.error(f"  {line}")
                logger.error("STDERR:")
                for line in result.stderr.split('\n')[-20:]:
                    if line.strip():
                        logger.error(f"  {line}")
                return False
                
    except subprocess.TimeoutExpired:
        logger.error("❌ Training script timed out (>5 minutes)")
        return False
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_import_only():
    """Test just the imports to verify syntax"""
    logger.info("Testing imports from train_improved_gan.py...")
    
    try:
        # Test the imports
        sys.path.append(str(Path(__file__).parent))
        
        from src.gan.fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator
        from src.gan.fixed_trainer import FixedGANTrainer, FixedGANConfig
        from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader
        
        logger.info("✅ All imports successful")
        
        # Test basic model creation
        config = FixedGANConfig()
        
        generator = DimensionFixedGenerator(
            noise_dim=128,
            numeric_features=['I1', 'I2'],
            categorical_features=['C1', 'C2'],
            vocab_sizes=[100, 200],
            embedding_dim=16
        )
        
        discriminator = DimensionFixedDiscriminator(
            numeric_features=['I1', 'I2'],
            categorical_features=['C1', 'C2'],
            vocab_sizes=[100, 200],
            embedding_dim=16,
            hidden_dim=256
        )
        
        logger.info("✅ Model creation successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    logger.info("🔧 Testing updated train_improved_gan.py...")
    
    # Test 1: Import test
    if not test_import_only():
        logger.error("❌ Import test failed")
        return False
    
    # Test 2: Full training test
    logger.info("Running full training test...")
    if test_train_improved_gan():
        logger.info("🎉 train_improved_gan.py test PASSED!")
        logger.info("")
        logger.info("✅ READY FOR YOUR COMMAND:")
        logger.info("python train_improved_gan.py --dataset_name Criteo --dataset_path /data/Criteo_x4 --output_dir /data/improved_gan --max_samples 1000000 --epochs 50")
        logger.info("")
        logger.info("🔧 FIXES APPLIED:")
        logger.info("  - Dimension mismatch issues: RESOLVED")
        logger.info("  - NaN discriminator outputs: RESOLVED")
        logger.info("  - Binary cross-entropy errors: RESOLVED")
        logger.info("  - Training stability: IMPROVED")
        return True
    else:
        logger.error("❌ Full training test failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
