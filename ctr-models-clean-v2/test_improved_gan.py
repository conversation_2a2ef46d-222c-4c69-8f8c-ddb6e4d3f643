#!/usr/bin/env python3
"""
Test script to verify the improved GAN implementation works
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent
sys.path.append(str(ROOT_DIR))

# Import improved modules
from src.gan.improved_data_prep import ImprovedGANDataProcessor

def test_categorical_handling():
    """Test the categorical data handling fix"""
    print("Testing categorical data handling...")
    
    # Create test data with categorical columns
    test_data = pd.DataFrame({
        'I1': [1.0, 2.0, np.nan, 4.0, 5.0],
        'I2': [0.5, 1.5, 2.5, np.nan, 4.5],
        'C1': pd.Categorical(['a', 'b', None, 'a', 'c']),
        'C2': pd.Categorical(['x', None, 'y', 'x', 'z']),
        'Label': [0, 1, 0, 1, 0]
    })
    
    print(f"Original data shape: {test_data.shape}")
    print(f"Original dtypes:\n{test_data.dtypes}")
    print(f"Original data:\n{test_data}")
    
    # Create processor
    processor = ImprovedGANDataProcessor(
        dataset_name='Test',
        data_dir='.',
        output_dir='./test_output',
        max_vocab_size=1000,
        min_freq=1
    )
    
    # Override feature definitions for test
    processor.numeric_features = ['I1', 'I2']
    processor.categorical_features = ['C1', 'C2']
    processor.label_col = 'Label'
    
    try:
        # Test preprocessing
        processed_data = processor.preprocess_for_gan(test_data, is_training=True)
        print(f"\nProcessed data shape: {processed_data.shape}")
        print(f"Processed data:\n{processed_data}")
        
        # Test reverse preprocessing
        reversed_data = processor.reverse_preprocessing(processed_data)
        print(f"\nReversed data shape: {reversed_data.shape}")
        print(f"Reversed data:\n{reversed_data}")
        
        print("\n✅ Categorical handling test PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Categorical handling test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_imports():
    """Test that all model imports work"""
    print("\nTesting model imports...")
    
    try:
        from src.gan.models import ImprovedConditionalGenerator, ImprovedDiscriminator
        from src.gan.improved_trainer import ImprovedGANTrainer, ImprovedGANConfig
        
        print("✅ Model imports PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Model imports FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """Test creating the improved models"""
    print("\nTesting model creation...")
    
    try:
        from src.gan.models import ImprovedConditionalGenerator, ImprovedDiscriminator
        from src.gan.improved_trainer import ImprovedGANConfig
        
        # Test parameters
        numeric_features = ['I1', 'I2']
        categorical_features = ['C1', 'C2']
        vocab_sizes = [10, 15]
        
        config = ImprovedGANConfig()
        
        # Create models
        generator = ImprovedConditionalGenerator(
            noise_dim=config.noise_dim,
            numeric_features=numeric_features,
            categorical_features=categorical_features,
            vocab_sizes=vocab_sizes,
            embedding_dim=config.embedding_dim,
            ctr_embedding_dim=config.ctr_embedding_dim
        )
        
        discriminator = ImprovedDiscriminator(
            numeric_features=numeric_features,
            categorical_features=categorical_features,
            vocab_sizes=vocab_sizes,
            embedding_dim=config.embedding_dim,
            hidden_dim=config.discriminator_hidden_dim
        )
        
        print(f"Generator parameters: {sum(p.numel() for p in generator.parameters()):,}")
        print(f"Discriminator parameters: {sum(p.numel() for p in discriminator.parameters()):,}")
        
        print("✅ Model creation test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Model creation test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Improved GAN Implementation")
    print("=" * 50)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        test_model_imports,
        test_categorical_handling,
        test_model_creation
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
        print("-" * 30)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! The improved GAN implementation is ready to use.")
        print("\nNext steps:")
        print("1. Run: python scripts/train_improved_gan.py --dataset_name Criteo --dataset_path /your/data/path")
        print("2. Monitor training metrics for improvements")
        print("3. Generate synthetic data and compare quality")
    else:
        print("❌ Some tests FAILED. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
