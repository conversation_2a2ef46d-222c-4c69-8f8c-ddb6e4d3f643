2025-07-04 12:29:37,560 [INFO] __main__: Starting Conditional GAN Training - Solving Asymmetric Training Problem
2025-07-04 12:29:37,560 [INFO] __main__: Arguments: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/conditional_gan', 'noise_dim': 128, 'embedding_dim': 16, 'ctr_embedding_dim': 8, 'epochs': 50, 'batch_size': 512, 'generator_lr': 0.0002, 'discriminator_lr': 0.0001, 'lambda_adversarial': 1.0, 'lambda_ctr_consistency': 1.0, 'lambda_feature_matching': 0.5, 'max_samples': 500000, 'max_vocab_size': 10000, 'seed': 2024, 'debug': False, 'resume': None}
2025-07-04 12:29:38,108 [INFO] __main__: Loading Criteo dataset from /data/Criteo_x4
2025-07-04 12:29:38,109 [INFO] root: Loading cached processed data from /data/conditional_gan/data/processed_Criteo_train_balanced_500000_10000.pkl
2025-07-04 12:29:38,287 [INFO] src.gan.data_prep: Max vocab size per feature: 10000
2025-07-04 12:29:38,287 [INFO] root: Loaded 500000 cached samples with max_vocab_size=10000
2025-07-04 12:29:38,288 [INFO] __main__: Final dataset size: 500000 samples
2025-07-04 12:29:38,288 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 12:29:38,288 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 12:29:38,288 [INFO] __main__: Feature info: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}
2025-07-04 12:29:38,290 [INFO] __main__: CTR rate in training data: 0.2517
2025-07-04 12:29:38,481 [INFO] __main__: Created conditional dataloader with 976 batches
2025-07-04 12:29:38,492 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 12:29:38,492 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 12:29:38,492 [INFO] __main__: Creating Conditional GAN models
2025-07-04 12:29:38,576 [INFO] __main__: Generator parameters: 10,597,424
2025-07-04 12:29:38,577 [INFO] __main__: Discriminator parameters: 1,971,330
2025-07-04 12:29:38,577 [INFO] __main__: Parameter ratio (G/D): 5.38
2025-07-04 12:29:38,577 [INFO] __main__: Learning rate ratio (G/D): 2.00
2025-07-04 12:29:39,275 [INFO] src.gan.conditional_trainer: Conditional GAN Trainer initialized on cuda
2025-07-04 12:29:39,276 [INFO] __main__: Starting Conditional GAN training loop
2025-07-04 12:29:39,276 [INFO] __main__: Key Innovation: Symmetric CTR supervision for real and fake data
2025-07-04 12:29:39,276 [INFO] __main__: Starting epoch 1/50
2025-07-04 12:29:39,670 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 0: D_loss=1.3364, G_loss=1.4027, D_CTR=0.6408, G_CTR=0.6319
2025-07-04 12:29:43,790 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 100: D_loss=0.5651, G_loss=1.6816, D_CTR=0.1244, G_CTR=0.1230
2025-07-04 12:29:47,893 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 200: D_loss=0.3585, G_loss=2.8772, D_CTR=0.0251, G_CTR=0.0240
2025-07-04 12:29:51,997 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 300: D_loss=0.3404, G_loss=2.9296, D_CTR=0.0095, G_CTR=0.0095
2025-07-04 12:29:56,096 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 400: D_loss=0.3351, G_loss=2.9664, D_CTR=0.0053, G_CTR=0.0050
2025-07-04 12:30:00,347 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 500: D_loss=0.3313, G_loss=2.9573, D_CTR=0.0030, G_CTR=0.0029
2025-07-04 12:30:04,477 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 600: D_loss=0.3298, G_loss=2.9805, D_CTR=0.0020, G_CTR=0.0018
2025-07-04 12:30:08,598 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 700: D_loss=0.3291, G_loss=2.9743, D_CTR=0.0014, G_CTR=0.0015
2025-07-04 12:30:12,849 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 800: D_loss=0.3285, G_loss=3.0005, D_CTR=0.0010, G_CTR=0.0009
2025-07-04 12:30:16,976 [INFO] src.gan.conditional_trainer: Epoch 0, Batch 900: D_loss=0.3285, G_loss=3.0762, D_CTR=0.0011, G_CTR=0.0013
2025-07-04 12:30:20,220 [INFO] __main__: Epoch 1 completed. Metrics: {'d_loss': 0.4028942137956619, 'd_adv_loss': 0.3597721531376487, 'd_ctr_loss': 0.04312205988652317, 'real_rf_score': 0.8579837517415891, 'fake_rf_score': 0.14693856892008028, 'real_ctr_consistency': 0.9636976668214212, 'fake_ctr_consistency': 0.9620762420360183, 'g_loss': 2.752891574604589, 'g_adv_loss': 2.134817656923513, 'g_ctr_loss': 0.04105768123643757, 'g_feature_loss': 1.1540324708569, 'fake_rf_score_g': 0.14116366855708545, 'fake_ctr_consistency_g': 0.9644193488066314, 'temperature': 0.5311054994437245}
2025-07-04 12:30:20,220 [INFO] __main__: Starting epoch 2/50
2025-07-04 12:30:20,281 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 0: D_loss=0.3280, G_loss=2.9972, D_CTR=0.0007, G_CTR=0.0007
2025-07-04 12:30:24,417 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 100: D_loss=0.3275, G_loss=3.0496, D_CTR=0.0006, G_CTR=0.0006
2025-07-04 12:30:28,550 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 200: D_loss=0.3276, G_loss=3.0751, D_CTR=0.0006, G_CTR=0.0007
2025-07-04 12:30:32,816 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 300: D_loss=0.3274, G_loss=3.0509, D_CTR=0.0004, G_CTR=0.0003
2025-07-04 12:30:36,962 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 400: D_loss=0.3273, G_loss=3.1570, D_CTR=0.0004, G_CTR=0.0006
2025-07-04 12:30:41,108 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 500: D_loss=0.3270, G_loss=3.1590, D_CTR=0.0003, G_CTR=0.0003
2025-07-04 12:30:45,379 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 600: D_loss=0.3269, G_loss=3.1414, D_CTR=0.0003, G_CTR=0.0003
2025-07-04 12:30:49,484 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 700: D_loss=0.3266, G_loss=3.1720, D_CTR=0.0002, G_CTR=0.0002
2025-07-04 12:30:53,699 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 800: D_loss=0.3265, G_loss=3.1256, D_CTR=0.0002, G_CTR=0.0002
2025-07-04 12:30:57,792 [INFO] src.gan.conditional_trainer: Epoch 1, Batch 900: D_loss=0.3264, G_loss=3.1405, D_CTR=0.0002, G_CTR=0.0002
2025-07-04 12:31:00,892 [INFO] __main__: Epoch 2 completed. Metrics: {'d_loss': 0.32707879191539324, 'd_adv_loss': 0.3267245282892321, 'd_ctr_loss': 0.0003542638833245232, 'real_rf_score': 0.8984502474548387, 'fake_rf_score': 0.10149133938257812, 'real_ctr_consistency': 0.999687068958263, 'fake_ctr_consistency': 0.9996046692621513, 'g_loss': 3.1239394951062125, 'g_adv_loss': 2.3258743481557875, 'g_ctr_loss': 0.00037798389672150464, 'g_feature_loss': 1.5953743284354445, 'fake_rf_score_g': 0.09894497290284174, 'fake_ctr_consistency_g': 0.9996221476524579, 'temperature': 0.5}
2025-07-04 12:31:00,893 [INFO] __main__: Starting epoch 3/50
2025-07-04 12:31:00,954 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 0: D_loss=0.3266, G_loss=3.2135, D_CTR=0.0002, G_CTR=0.0002
2025-07-04 12:31:05,172 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 100: D_loss=0.3265, G_loss=3.1928, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:09,278 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 200: D_loss=0.3265, G_loss=3.2315, D_CTR=0.0001, G_CTR=0.0002
2025-07-04 12:31:13,372 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 300: D_loss=0.3261, G_loss=3.1586, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:17,590 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 400: D_loss=0.3262, G_loss=3.1740, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:21,680 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 500: D_loss=0.3263, G_loss=3.2474, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:25,888 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 600: D_loss=0.3263, G_loss=3.2074, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:29,982 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 700: D_loss=0.3261, G_loss=3.1995, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:34,085 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 800: D_loss=0.3262, G_loss=3.3233, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:38,291 [INFO] src.gan.conditional_trainer: Epoch 2, Batch 900: D_loss=0.3262, G_loss=3.2681, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:41,379 [INFO] __main__: Epoch 3 completed. Metrics: {'d_loss': 0.3262998856543029, 'd_adv_loss': 0.3261920674475002, 'd_ctr_loss': 0.00010781839746861626, 'real_rf_score': 0.898884577523978, 'fake_rf_score': 0.10114183144827114, 'real_ctr_consistency': 0.999890072118552, 'fake_ctr_consistency': 0.9998943233465563, 'g_loss': 3.229797472475005, 'g_adv_loss': 2.3126895452131992, 'g_ctr_loss': 0.00010391423322408762, 'g_feature_loss': 1.8340080243642214, 'fake_rf_score_g': 0.09984233090654016, 'fake_ctr_consistency_g': 0.9998960970122306, 'temperature': 0.5}
2025-07-04 12:31:41,380 [INFO] __main__: Starting epoch 4/50
2025-07-04 12:31:41,440 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 0: D_loss=0.3260, G_loss=3.2719, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:45,545 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 100: D_loss=0.3259, G_loss=3.2580, D_CTR=0.0001, G_CTR=0.0000
2025-07-04 12:31:49,775 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 200: D_loss=0.3260, G_loss=3.3154, D_CTR=0.0001, G_CTR=0.0000
2025-07-04 12:31:53,895 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 300: D_loss=0.3261, G_loss=3.3710, D_CTR=0.0001, G_CTR=0.0001
2025-07-04 12:31:58,133 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 400: D_loss=0.3259, G_loss=3.2470, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:02,243 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 500: D_loss=0.3260, G_loss=3.2232, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:06,357 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 600: D_loss=0.3258, G_loss=3.3012, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:10,602 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 700: D_loss=0.3260, G_loss=3.2901, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:14,721 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 800: D_loss=0.3263, G_loss=3.2372, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:18,949 [INFO] src.gan.conditional_trainer: Epoch 3, Batch 900: D_loss=0.3259, G_loss=3.3089, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:22,050 [INFO] __main__: Epoch 4 completed. Metrics: {'d_loss': 0.3259822469143594, 'd_adv_loss': 0.3259333007526202, 'd_ctr_loss': 4.8945897978243365e-05, 'real_rf_score': 0.8991325544651414, 'fake_rf_score': 0.10093626409738524, 'real_ctr_consistency': 0.9999420527429854, 'fake_ctr_consistency': 0.9999600642772971, 'g_loss': 3.2817832688327697, 'g_adv_loss': 2.30707483408881, 'g_ctr_loss': 3.861787614055845e-05, 'g_feature_loss': 1.949339636769451, 'fake_rf_score_g': 0.1002001149931037, 'fake_ctr_consistency_g': 0.9999613825415001, 'temperature': 0.5}
2025-07-04 12:32:22,050 [INFO] __main__: Starting epoch 5/50
2025-07-04 12:32:22,111 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 0: D_loss=0.3258, G_loss=3.4951, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:26,228 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 100: D_loss=0.3257, G_loss=3.3517, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:30,459 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 200: D_loss=0.3257, G_loss=3.2404, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:34,564 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 300: D_loss=0.3258, G_loss=3.3148, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:38,667 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 400: D_loss=0.3257, G_loss=3.2577, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:42,876 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 500: D_loss=0.3256, G_loss=3.2852, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:46,970 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 600: D_loss=0.3257, G_loss=3.3041, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:51,174 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 700: D_loss=0.3257, G_loss=3.3668, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:55,265 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 800: D_loss=0.3257, G_loss=3.3298, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:32:59,356 [INFO] src.gan.conditional_trainer: Epoch 4, Batch 900: D_loss=0.3257, G_loss=3.2959, D_CTR=0.0000, G_CTR=0.0000
2025-07-04 12:33:02,563 [INFO] __main__: Epoch 5 completed. Metrics: {'d_loss': 0.32580483460524046, 'd_adv_loss': 0.3257791675688302, 'd_ctr_loss': 2.5667118796852757e-05, 'real_rf_score': 0.8992797331121124, 'fake_rf_score': 0.1007578454942244, 'real_ctr_consistency': 0.9999656060435733, 'fake_ctr_consistency': 0.9999830639142482, 'g_loss': 3.315525371031683, 'g_adv_loss': 2.3039847580624406, 'g_ctr_loss': 1.5870758589386e-05, 'g_feature_loss': 2.02304948805297, 'fake_rf_score_g': 0.10036832239234546, 'fake_ctr_consistency_g': 0.9999841278205153, 'temperature': 0.5}
2025-07-04 12:33:02,563 [INFO] __main__: Evaluating Conditional GAN at epoch 5
2025-07-04 12:33:02,589 [INFO] __main__: Conditional GAN Evaluation:
2025-07-04 12:33:02,589 [INFO] __main__:   CTR=0 samples - RF score: 0.098, Consistency: 1.000
2025-07-04 12:33:02,589 [INFO] __main__:   CTR=1 samples - RF score: 0.098, Consistency: 1.000
2025-07-04 12:33:02,589 [INFO] __main__:   Average CTR consistency: 1.000
2025-07-04 12:33:02,590 [INFO] __main__: New best CTR consistency: 1.000
2025-07-04 12:33:02,780 [INFO] src.gan.conditional_trainer: Checkpoint saved to /data/conditional_gan/conditional_checkpoint_epoch_5.pt
2025-07-04 12:33:02,969 [INFO] src.gan.conditional_trainer: Checkpoint saved to /data/conditional_gan/conditional_best_model.pt
2025-07-04 12:33:02,970 [INFO] __main__: Saved best model with CTR consistency: 1.000
2025-07-04 12:33:02,970 [INFO] __main__: Starting epoch 6/50
2025-07-04 12:33:03,036 [INFO] src.gan.conditional_trainer: Epoch 5, Batch 0: D_loss=0.3257, G_loss=3.2770, D_CTR=0.0000, G_CTR=0.0000
