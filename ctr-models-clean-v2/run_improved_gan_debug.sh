#!/bin/bash

# Run improved GAN training with CUDA debugging enabled
# This will help identify the exact source of CUDA errors

echo "🚀 Starting Improved GAN Training with CUDA Debugging"
echo "=================================================="

# Enable CUDA debugging
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1

# Set Python to show full tracebacks
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Run training with debug settings
python scripts/train_improved_gan.py \
    --dataset_name Criteo \
    --dataset_path /data/Criteo_x4 \
    --output_dir /data/improved_gan_debug \
    --max_samples 100000 \
    --epochs 5 \
    --batch_size 256 \
    --debug \
    2>&1 | tee /data/improved_gan_debug/training_debug.log

echo "Training completed. Check /data/improved_gan_debug/training_debug.log for details."
