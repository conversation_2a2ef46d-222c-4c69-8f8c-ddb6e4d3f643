#!/usr/bin/env python3
"""
验证改进的GAN实现
比较改进前后的合成数据质量，验证修复效果
"""

import os
import sys
import logging
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from scipy import stats
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Validate Improved GAN Implementation')
    
    # 数据路径
    parser.add_argument('--real_data_path', type=str, required=True,
                       help='Path to real data (for comparison)')
    parser.add_argument('--old_synthetic_path', type=str, required=True,
                       help='Path to old synthetic data')
    parser.add_argument('--new_synthetic_path', type=str, required=True,
                       help='Path to new improved synthetic data')
    
    # 输出
    parser.add_argument('--output_dir', type=str, default='./validation_results',
                       help='Output directory for validation results')
    
    # 验证参数
    parser.add_argument('--sample_size', type=int, default=50000,
                       help='Sample size for comparison (to ensure fair comparison)')
    parser.add_argument('--test_size', type=float, default=0.2,
                       help='Test size for CTR model validation')
    
    # 其他
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    
    return parser.parse_args()


def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(os.path.join(output_dir, 'validation.log'))
        ]
    )
    return logging.getLogger(__name__)


def load_and_sample_data(data_path: str, sample_size: int, seed: int) -> pd.DataFrame:
    """加载并采样数据"""
    df = pd.read_csv(data_path)
    
    if len(df) > sample_size:
        df = df.sample(n=sample_size, random_state=seed)
    
    return df.reset_index(drop=True)


def analyze_feature_distributions(real_df: pd.DataFrame, old_synthetic_df: pd.DataFrame, 
                                new_synthetic_df: pd.DataFrame, output_dir: str, logger):
    """分析特征分布"""
    logger.info("Analyzing feature distributions...")
    
    # 识别数值特征和类别特征
    numeric_features = []
    categorical_features = []
    
    for col in real_df.columns:
        if col.lower() in ['label', 'click', 'ctr']:
            continue
        
        if real_df[col].dtype in ['int64', 'float64'] and real_df[col].nunique() > 20:
            numeric_features.append(col)
        else:
            categorical_features.append(col)
    
    logger.info(f"Found {len(numeric_features)} numeric features and {len(categorical_features)} categorical features")
    
    # 分析数值特征分布
    if numeric_features:
        analyze_numeric_distributions(
            real_df[numeric_features], 
            old_synthetic_df[numeric_features], 
            new_synthetic_df[numeric_features],
            output_dir, logger
        )
    
    # 分析类别特征分布
    if categorical_features:
        analyze_categorical_distributions(
            real_df[categorical_features], 
            old_synthetic_df[categorical_features], 
            new_synthetic_df[categorical_features],
            output_dir, logger
        )


def analyze_numeric_distributions(real_numeric: pd.DataFrame, old_synthetic_numeric: pd.DataFrame,
                                new_synthetic_numeric: pd.DataFrame, output_dir: str, logger):
    """分析数值特征分布"""
    logger.info("Analyzing numeric feature distributions...")
    
    results = []
    
    for col in real_numeric.columns:
        if col not in old_synthetic_numeric.columns or col not in new_synthetic_numeric.columns:
            continue
        
        real_data = real_numeric[col].dropna()
        old_data = old_synthetic_numeric[col].dropna()
        new_data = new_synthetic_numeric[col].dropna()
        
        # 计算统计量
        real_stats = {
            'mean': real_data.mean(),
            'std': real_data.std(),
            'min': real_data.min(),
            'max': real_data.max(),
            'skew': stats.skew(real_data),
            'kurtosis': stats.kurtosis(real_data)
        }
        
        old_stats = {
            'mean': old_data.mean(),
            'std': old_data.std(),
            'min': old_data.min(),
            'max': old_data.max(),
            'skew': stats.skew(old_data),
            'kurtosis': stats.kurtosis(old_data)
        }
        
        new_stats = {
            'mean': new_data.mean(),
            'std': new_data.std(),
            'min': new_data.min(),
            'max': new_data.max(),
            'skew': stats.skew(new_data),
            'kurtosis': stats.kurtosis(new_data)
        }
        
        # KS测试
        old_ks_stat, old_ks_p = stats.ks_2samp(real_data, old_data)
        new_ks_stat, new_ks_p = stats.ks_2samp(real_data, new_data)
        
        results.append({
            'feature': col,
            'real_mean': real_stats['mean'],
            'real_std': real_stats['std'],
            'old_mean': old_stats['mean'],
            'old_std': old_stats['std'],
            'new_mean': new_stats['mean'],
            'new_std': new_stats['std'],
            'old_ks_stat': old_ks_stat,
            'old_ks_p': old_ks_p,
            'new_ks_stat': new_ks_stat,
            'new_ks_p': new_ks_p,
            'old_mean_diff': abs(old_stats['mean'] - real_stats['mean']),
            'new_mean_diff': abs(new_stats['mean'] - real_stats['mean']),
            'old_std_diff': abs(old_stats['std'] - real_stats['std']),
            'new_std_diff': abs(new_stats['std'] - real_stats['std'])
        })
    
    # 保存结果
    results_df = pd.DataFrame(results)
    results_df.to_csv(os.path.join(output_dir, 'numeric_distribution_analysis.csv'), index=False)
    
    # 计算总体改进
    old_mean_error = results_df['old_mean_diff'].mean()
    new_mean_error = results_df['new_mean_diff'].mean()
    old_std_error = results_df['old_std_diff'].mean()
    new_std_error = results_df['new_std_diff'].mean()
    
    logger.info(f"Numeric features analysis:")
    logger.info(f"  Mean error - Old: {old_mean_error:.6f}, New: {new_mean_error:.6f}, Improvement: {((old_mean_error - new_mean_error) / old_mean_error * 100):.2f}%")
    logger.info(f"  Std error - Old: {old_std_error:.6f}, New: {new_std_error:.6f}, Improvement: {((old_std_error - new_std_error) / old_std_error * 100):.2f}%")


def analyze_categorical_distributions(real_categorical: pd.DataFrame, old_synthetic_categorical: pd.DataFrame,
                                    new_synthetic_categorical: pd.DataFrame, output_dir: str, logger):
    """分析类别特征分布"""
    logger.info("Analyzing categorical feature distributions...")
    
    results = []
    
    for col in real_categorical.columns:
        if col not in old_synthetic_categorical.columns or col not in new_synthetic_categorical.columns:
            continue
        
        real_counts = real_categorical[col].value_counts(normalize=True)
        old_counts = old_synthetic_categorical[col].value_counts(normalize=True)
        new_counts = new_synthetic_categorical[col].value_counts(normalize=True)
        
        # 计算JS散度
        def js_divergence(p, q):
            # 确保相同的索引
            all_keys = set(p.index) | set(q.index)
            p_aligned = pd.Series([p.get(k, 0) for k in all_keys], index=all_keys)
            q_aligned = pd.Series([q.get(k, 0) for k in all_keys], index=all_keys)
            
            # 添加小的平滑项
            p_aligned += 1e-10
            q_aligned += 1e-10
            
            # 归一化
            p_aligned /= p_aligned.sum()
            q_aligned /= q_aligned.sum()
            
            m = (p_aligned + q_aligned) / 2
            return (stats.entropy(p_aligned, m) + stats.entropy(q_aligned, m)) / 2
        
        old_js = js_divergence(real_counts, old_counts)
        new_js = js_divergence(real_counts, new_counts)
        
        results.append({
            'feature': col,
            'real_unique': len(real_counts),
            'old_unique': len(old_counts),
            'new_unique': len(new_counts),
            'old_js_divergence': old_js,
            'new_js_divergence': new_js
        })
    
    # 保存结果
    results_df = pd.DataFrame(results)
    results_df.to_csv(os.path.join(output_dir, 'categorical_distribution_analysis.csv'), index=False)
    
    # 计算总体改进
    old_js_avg = results_df['old_js_divergence'].mean()
    new_js_avg = results_df['new_js_divergence'].mean()
    
    logger.info(f"Categorical features analysis:")
    logger.info(f"  JS divergence - Old: {old_js_avg:.6f}, New: {new_js_avg:.6f}, Improvement: {((old_js_avg - new_js_avg) / old_js_avg * 100):.2f}%")


def analyze_ctr_relationships(real_df: pd.DataFrame, old_synthetic_df: pd.DataFrame,
                            new_synthetic_df: pd.DataFrame, output_dir: str, logger):
    """分析CTR关系"""
    logger.info("Analyzing CTR relationships...")
    
    # 识别标签列
    label_col = None
    for col in ['Label', 'label', 'click', 'Click', 'ctr', 'CTR']:
        if col in real_df.columns:
            label_col = col
            break
    
    if label_col is None:
        logger.warning("No label column found, skipping CTR analysis")
        return
    
    # CTR率比较
    real_ctr = real_df[label_col].mean()
    old_ctr = old_synthetic_df[label_col].mean()
    new_ctr = new_synthetic_df[label_col].mean()
    
    logger.info(f"CTR rates:")
    logger.info(f"  Real: {real_ctr:.4f}")
    logger.info(f"  Old synthetic: {old_ctr:.4f} (diff: {abs(old_ctr - real_ctr):.4f})")
    logger.info(f"  New synthetic: {new_ctr:.4f} (diff: {abs(new_ctr - real_ctr):.4f})")
    
    # 训练简单的CTR预测模型来评估特征-CTR关系质量
    evaluate_ctr_prediction_quality(real_df, old_synthetic_df, new_synthetic_df, label_col, output_dir, logger)


def evaluate_ctr_prediction_quality(real_df: pd.DataFrame, old_synthetic_df: pd.DataFrame,
                                   new_synthetic_df: pd.DataFrame, label_col: str, 
                                   output_dir: str, logger):
    """评估CTR预测质量"""
    logger.info("Evaluating CTR prediction quality...")
    
    # 准备特征
    feature_cols = [col for col in real_df.columns if col != label_col]
    
    # 只使用数值特征进行简单测试
    numeric_cols = []
    for col in feature_cols:
        if real_df[col].dtype in ['int64', 'float64']:
            numeric_cols.append(col)
    
    if len(numeric_cols) == 0:
        logger.warning("No numeric features found for CTR prediction test")
        return
    
    logger.info(f"Using {len(numeric_cols)} numeric features for CTR prediction test")
    
    # 准备数据
    def prepare_data(df):
        X = df[numeric_cols].fillna(0)
        y = df[label_col]
        return X, y
    
    # 真实数据
    X_real, y_real = prepare_data(real_df)
    X_real_train, X_real_test, y_real_train, y_real_test = train_test_split(
        X_real, y_real, test_size=0.2, random_state=42, stratify=y_real
    )
    
    # 标准化
    scaler = StandardScaler()
    X_real_train_scaled = scaler.fit_transform(X_real_train)
    X_real_test_scaled = scaler.transform(X_real_test)
    
    # 在真实数据上训练模型
    model_real = LogisticRegression(random_state=42, max_iter=1000)
    model_real.fit(X_real_train_scaled, y_real_train)
    
    # 在真实测试集上评估
    y_real_pred = model_real.predict_proba(X_real_test_scaled)[:, 1]
    auc_real = roc_auc_score(y_real_test, y_real_pred)
    
    # 在合成数据上训练模型
    X_old, y_old = prepare_data(old_synthetic_df)
    X_new, y_new = prepare_data(new_synthetic_df)
    
    X_old_scaled = scaler.fit_transform(X_old)
    X_new_scaled = scaler.fit_transform(X_new)
    
    model_old = LogisticRegression(random_state=42, max_iter=1000)
    model_new = LogisticRegression(random_state=42, max_iter=1000)
    
    model_old.fit(X_old_scaled, y_old)
    model_new.fit(X_new_scaled, y_new)
    
    # 在真实测试集上评估合成数据训练的模型
    y_old_pred = model_old.predict_proba(X_real_test_scaled)[:, 1]
    y_new_pred = model_new.predict_proba(X_real_test_scaled)[:, 1]
    
    auc_old = roc_auc_score(y_real_test, y_old_pred)
    auc_new = roc_auc_score(y_real_test, y_new_pred)
    
    logger.info(f"CTR prediction AUC results:")
    logger.info(f"  Real data (baseline): {auc_real:.4f}")
    logger.info(f"  Old synthetic: {auc_old:.4f} (gap: {auc_real - auc_old:.4f})")
    logger.info(f"  New synthetic: {auc_new:.4f} (gap: {auc_real - auc_new:.4f})")
    logger.info(f"  Improvement: {auc_new - auc_old:.4f} ({((auc_new - auc_old) / (auc_real - auc_old) * 100):.2f}% of gap closed)")
    
    # 保存结果
    results = {
        'auc_real': auc_real,
        'auc_old_synthetic': auc_old,
        'auc_new_synthetic': auc_new,
        'old_gap': auc_real - auc_old,
        'new_gap': auc_real - auc_new,
        'improvement': auc_new - auc_old,
        'gap_closed_percent': ((auc_new - auc_old) / (auc_real - auc_old) * 100) if auc_real - auc_old > 0 else 0
    }
    
    import json
    with open(os.path.join(output_dir, 'ctr_prediction_results.json'), 'w') as f:
        json.dump(results, f, indent=2)


def main():
    """主函数"""
    args = parse_args()
    logger = setup_logging(args.output_dir)
    
    np.random.seed(args.seed)
    
    logger.info("Starting GAN improvement validation...")
    logger.info(f"Real data: {args.real_data_path}")
    logger.info(f"Old synthetic: {args.old_synthetic_path}")
    logger.info(f"New synthetic: {args.new_synthetic_path}")
    
    try:
        # 加载数据
        logger.info("Loading data...")
        real_df = load_and_sample_data(args.real_data_path, args.sample_size, args.seed)
        old_synthetic_df = load_and_sample_data(args.old_synthetic_path, args.sample_size, args.seed)
        new_synthetic_df = load_and_sample_data(args.new_synthetic_path, args.sample_size, args.seed)
        
        logger.info(f"Data shapes - Real: {real_df.shape}, Old: {old_synthetic_df.shape}, New: {new_synthetic_df.shape}")
        
        # 分析特征分布
        analyze_feature_distributions(real_df, old_synthetic_df, new_synthetic_df, args.output_dir, logger)
        
        # 分析CTR关系
        analyze_ctr_relationships(real_df, old_synthetic_df, new_synthetic_df, args.output_dir, logger)
        
        logger.info(f"Validation completed! Results saved to {args.output_dir}")
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        logger.error(f"Error details:", exc_info=True)
        raise


if __name__ == "__main__":
    main()
