#!/usr/bin/env python3
"""
改进的GAN训练脚本 - 解决所有关键问题
主要改进：
1. 使用条件生成器，解决循环CTR依赖
2. 改进的数据预处理，保持统计特性
3. 更强的判别器架构
4. 优化的训练策略和超参数
5. 更好的特征质量控制
"""

import os
import sys
import logging
import argparse
import json
import wandb
import torch
import numpy as np
from datetime import datetime
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

# Import FIXED modules (updated to resolve dimension mismatch and NaN issues)
from src.gan.improved_data_prep import prepare_improved_gan_data, create_improved_dataloader, ImprovedGANDataProcessor
from src.gan.fixed_models import DimensionFixedGenerator, DimensionFixedDiscriminator
from src.gan.fixed_trainer import FixedGANTrainer, FixedGANConfig

# Import existing utilities
from src.data_process.utils import seed_everything


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Improved GAN Training - Fix All Critical Issues')
    
    # 数据集相关
    parser.add_argument('--dataset_name', type=str, default='Criteo',
                       choices=['Criteo', 'Avazu'],
                       help='Dataset name')
    parser.add_argument('--dataset_path', type=str, default='/data/Criteo_x4',
                       help='Path to dataset directory')
    parser.add_argument('--output_dir', type=str, default='/data/improved_gan',
                       help='Output directory for models and logs')
    
    # 模型参数
    parser.add_argument('--noise_dim', type=int, default=128,
                       help='Dimension of input noise')
    parser.add_argument('--embedding_dim', type=int, default=16,
                       help='Dimension of categorical embeddings')
    parser.add_argument('--ctr_embedding_dim', type=int, default=8,
                       help='Dimension of CTR label embeddings')
    parser.add_argument('--discriminator_hidden_dim', type=int, default=256,
                       help='Hidden dimension of discriminator')
    
    # 训练参数 - 优化的设置
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=512,
                       help='Batch size')
    parser.add_argument('--generator_lr', type=float, default=1e-4,
                       help='Generator learning rate - balanced')
    parser.add_argument('--discriminator_lr', type=float, default=2e-4,
                       help='Discriminator learning rate - stronger')
    
    # 损失权重
    parser.add_argument('--lambda_adversarial', type=float, default=1.0,
                       help='Adversarial loss weight')
    parser.add_argument('--lambda_ctr_consistency', type=float, default=2.0,
                       help='CTR consistency loss weight')
    parser.add_argument('--lambda_feature_matching', type=float, default=0.5,
                       help='Feature matching loss weight')
    parser.add_argument('--lambda_diversity', type=float, default=0.1,
                       help='Diversity loss weight')
    
    # 训练策略
    parser.add_argument('--d_steps', type=int, default=2,
                       help='Discriminator training steps per iteration')
    parser.add_argument('--g_steps', type=int, default=1,
                       help='Generator training steps per iteration')
    
    # 温度控制
    parser.add_argument('--initial_temperature', type=float, default=2.0,
                       help='Initial temperature for Gumbel softmax')
    parser.add_argument('--min_temperature', type=float, default=0.5,
                       help='Minimum temperature')
    parser.add_argument('--temperature_decay', type=float, default=0.995,
                       help='Temperature decay rate')
    
    # 数据控制参数 - 改进的设置
    parser.add_argument('--max_vocab_size', type=int, default=20000,
                       help='Maximum vocabulary size per categorical feature')
    parser.add_argument('--min_freq', type=int, default=5,
                       help='Minimum frequency for categorical values')
    parser.add_argument('--max_samples', type=int, default=1000000,
                       help='Maximum number of samples to use - increased')
    
    # 正则化
    parser.add_argument('--max_grad_norm', type=float, default=1.0,
                       help='Maximum gradient norm for clipping')
    parser.add_argument('--label_smoothing', type=float, default=0.1,
                       help='Label smoothing for discriminator')
    
    # 日志和保存
    parser.add_argument('--log_interval', type=int, default=100,
                       help='Logging interval (batches)')
    parser.add_argument('--save_interval', type=int, default=5,
                       help='Model saving interval (epochs)')
    parser.add_argument('--eval_interval', type=int, default=5,
                       help='Evaluation interval (epochs)')
    
    # 其他
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    parser.add_argument('--num_workers', type=int, default=2,
                       help='Number of data loading workers')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode (smaller dataset)')
    parser.add_argument('--force_cpu', action='store_true',
                       help='Force CPU training to debug CUDA issues')
    
    return parser.parse_args()


def setup_logging(output_dir, debug=False):
    """设置日志系统"""
    os.makedirs(output_dir, exist_ok=True)
    
    log_level = logging.DEBUG if debug else logging.INFO
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(output_dir, f'improved_gan_training_{timestamp}.log')
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )
    
    return logging.getLogger(__name__)


def create_config_from_args(args):
    """从命令行参数创建配置对象"""
    config = FixedGANConfig()  # Use the fixed config class
    
    # 更新配置
    config.noise_dim = args.noise_dim
    config.embedding_dim = args.embedding_dim
    config.ctr_embedding_dim = args.ctr_embedding_dim
    config.discriminator_hidden_dim = args.discriminator_hidden_dim
    config.generator_lr = args.generator_lr
    config.discriminator_lr = args.discriminator_lr
    config.batch_size = args.batch_size
    config.epochs = args.epochs
    config.lambda_adversarial = args.lambda_adversarial
    config.lambda_ctr_consistency = args.lambda_ctr_consistency
    config.lambda_feature_matching = args.lambda_feature_matching
    config.lambda_diversity = args.lambda_diversity
    config.d_steps = args.d_steps
    config.g_steps = args.g_steps
    config.initial_temperature = args.initial_temperature
    config.min_temperature = args.min_temperature
    config.temperature_decay = args.temperature_decay
    config.max_grad_norm = args.max_grad_norm
    config.label_smoothing = args.label_smoothing
    config.log_interval = args.log_interval
    config.save_interval = args.save_interval
    
    return config


def load_and_prepare_data(args, logger):
    """加载和预处理数据 - 使用改进的预处理器"""
    logger.info(f"加载 {args.dataset_name} 数据集从 {args.dataset_path}")
    
    # 创建输出目录
    gan_data_dir = os.path.join(args.output_dir, 'data')
    os.makedirs(gan_data_dir, exist_ok=True)
    
    # 使用更多的数据量
    max_samples = args.max_samples
    if args.debug:
        max_samples = 10000
        logger.info(f"调试模式：使用 {max_samples} 样本")
    
    # 准备改进的GAN训练数据
    processed_data, processor = prepare_improved_gan_data(
        dataset_name=args.dataset_name,
        data_dir=args.dataset_path,
        output_dir=gan_data_dir,
        train_file='train.csv',
        use_cache=True,
        max_samples=max_samples,
        max_vocab_size=args.max_vocab_size,
        min_freq=args.min_freq
    )
    
    logger.info(f"最终数据集大小: {len(processed_data)} 样本")
    logger.info(f"特征信息: {processor.get_feature_info()}")
    
    # 创建数据加载器
    config = create_config_from_args(args)
    dataloader = create_improved_dataloader(processed_data, config)
    
    logger.info(f"创建数据加载器，共 {len(dataloader)} 批次")
    
    return dataloader, processor


def create_improved_models(processor, config, logger):
    """创建改进的GAN模型"""
    feature_info = processor.get_feature_info()
    
    logger.info("创建改进的条件Generator和强化Discriminator模型")
    
    # 创建条件生成器 (using FIXED models)
    generator = DimensionFixedGenerator(
        noise_dim=config.noise_dim,
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim
    )

    # 创建改进的判别器 (using FIXED models)
    discriminator = DimensionFixedDiscriminator(
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim,
        hidden_dim=config.discriminator_hidden_dim
    )
    
    # 统计参数量
    gen_params = sum(p.numel() for p in generator.parameters() if p.requires_grad)
    disc_params = sum(p.numel() for p in discriminator.parameters() if p.requires_grad)
    
    logger.info(f"Generator参数量: {gen_params:,}")
    logger.info(f"Discriminator参数量: {disc_params:,}")
    logger.info(f"参数比例 (G/D): {gen_params/disc_params:.2f}")
    logger.info(f"学习率比例 (G/D): {config.generator_lr/config.discriminator_lr:.2f}")
    
    return generator, discriminator


def evaluate_improved_gan(generator, discriminator, processor, config, epoch, logger):
    """评估改进的GAN模型"""
    logger.info(f"在第{epoch}个epoch评估改进的GAN")
    
    generator.eval()
    discriminator.eval()
    
    device = next(generator.parameters()).device
    
    # 生成样本进行评估
    num_samples = 1000
    noise = torch.randn(num_samples, config.noise_dim, device=device)
    
    # 生成不同CTR标签的样本
    ctr_labels_0 = torch.zeros(num_samples // 2, device=device, dtype=torch.long)
    ctr_labels_1 = torch.ones(num_samples // 2, device=device, dtype=torch.long)
    ctr_labels = torch.cat([ctr_labels_0, ctr_labels_1])
    
    with torch.no_grad():
        # 生成数据
        gen_output = generator(
            noise=noise, 
            ctr_labels=ctr_labels,
            temperature=config.min_temperature, 
            hard=True
        )
        
        # 通过判别器评估
        fake_rf_score, fake_ctr_pred, _ = discriminator(
            numeric_data=gen_output['numeric'],
            categorical_embeddings=gen_output['categorical_embeddings']
        )
        
        # 计算CTR一致性
        ctr_pred_binary = (fake_ctr_pred.squeeze() > 0.5).float()
        ctr_consistency = (ctr_pred_binary == ctr_labels.float()).float().mean().item()
        
        # 计算评估指标
        metrics = {
            'eval_fake_rf_score_mean': fake_rf_score.mean().item(),
            'eval_fake_rf_score_std': fake_rf_score.std().item(),
            'eval_ctr_consistency': ctr_consistency,
            'eval_epoch': epoch
        }
        
        # 检查数值特征质量
        if gen_output['numeric'] is not None:
            numeric_data = gen_output['numeric'].cpu().numpy()
            
            # 基本统计
            feature_stds = np.std(numeric_data, axis=0)
            metrics.update({
                'eval_numeric_mean': float(np.mean(numeric_data)),
                'eval_numeric_std': float(np.std(numeric_data)),
                'eval_feature_std_avg': float(np.mean(feature_stds)),
                'eval_feature_std_min': float(np.min(feature_stds)),
                'eval_zero_std_features': float(np.sum(feature_stds < 1e-6))
            })
        
        # 记录到wandb
        wandb.log(metrics)
        
        logger.info(f"改进GAN评估指标:")
        logger.info(f"  CTR一致性: {ctr_consistency:.3f}")
        logger.info(f"  特征平均标准差: {metrics.get('eval_feature_std_avg', 0.0):.6f}")
        logger.info(f"  零标准差特征数: {metrics.get('eval_zero_std_features', 0.0)}")
    
    generator.train()
    discriminator.train()
    
    return metrics


def main():
    """主训练函数"""
    # 解析参数
    args = parse_args()
    
    # 设置种子
    seed_everything(args.seed)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置日志
    logger = setup_logging(args.output_dir, args.debug)
    logger.info("开始改进的GAN训练 - 解决所有关键问题")
    logger.info(f"参数: {vars(args)}")
    
    # 初始化wandb
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    wandb.init(
        project=f"improved-gan-{args.dataset_name.lower()}",
        name=f"improved_gan_{args.dataset_name}_{timestamp}_seed{args.seed}",
        config=vars(args),
        dir=args.output_dir
    )
    
    try:
        # 1. 加载和预处理数据
        dataloader, processor = load_and_prepare_data(args, logger)
        
        # 2. 创建改进的模型
        config = create_config_from_args(args)
        generator, discriminator = create_improved_models(processor, config, logger)
        
        # 3. 预初始化判别器以避免训练中的重新初始化
        logger.info("Pre-initializing discriminator with sample data...")
        sample_batch = next(iter(dataloader))
        sample_numeric, sample_categorical, sample_labels = sample_batch

        # 初始化判别器（真实数据）
        with torch.no_grad():
            _ = discriminator(numeric_data=sample_numeric, categorical_data=sample_categorical)

        # 初始化判别器（生成器数据）
        sample_noise = torch.randn(sample_labels.size(0), config.noise_dim)
        sample_ctr_labels = torch.randint(0, 2, (sample_labels.size(0),))
        with torch.no_grad():
            sample_gen_output = generator(noise=sample_noise, ctr_labels=sample_ctr_labels, temperature=1.0, hard=True)
            _ = discriminator(numeric_data=sample_gen_output['numeric'], categorical_embeddings=sample_gen_output['categorical_embeddings'])

        logger.info("Discriminator pre-initialization completed")

        # 4. 创建改进的训练器 (using FIXED trainer)
        device = torch.device('cpu' if args.force_cpu else ('cuda' if torch.cuda.is_available() else 'cpu'))
        trainer = FixedGANTrainer(generator, discriminator, config, device)
        
        # 4. 保存配置
        config_path = os.path.join(args.output_dir, 'improved_config.json')
        with open(config_path, 'w') as f:
            json.dump(vars(args), f, indent=2)
        
        # 5. 训练循环
        logger.info("开始改进的训练循环")
        best_ctr_consistency = 0.0
        
        for epoch in range(config.epochs):
            logger.info(f"开始第{epoch + 1}/{config.epochs}个epoch")
            
            # 训练一个epoch
            epoch_metrics = trainer.train_epoch(dataloader)
            
            logger.info(f"第{epoch + 1}个epoch完成. 指标: {epoch_metrics}")
            
            # 评估模型
            if (epoch + 1) % args.eval_interval == 0:
                eval_metrics = evaluate_improved_gan(
                    generator, discriminator, processor, config, epoch + 1, logger
                )
                
                # 更新最佳CTR一致性
                ctr_consistency = eval_metrics.get('eval_ctr_consistency', 0.0)
                if ctr_consistency > best_ctr_consistency:
                    best_ctr_consistency = ctr_consistency
                    logger.info(f"新的最佳CTR一致性: {best_ctr_consistency:.3f}")
            
            # 保存检查点
            if (epoch + 1) % config.save_interval == 0:
                checkpoint_path = os.path.join(args.output_dir, f'improved_checkpoint_epoch_{epoch + 1}.pt')
                torch.save({
                    'epoch': epoch + 1,
                    'generator_state_dict': generator.state_dict(),
                    'discriminator_state_dict': discriminator.state_dict(),
                    'g_optimizer_state_dict': trainer.g_optimizer.state_dict(),
                    'd_optimizer_state_dict': trainer.d_optimizer.state_dict(),
                    'config': config,
                    'metrics': epoch_metrics
                }, checkpoint_path)
                
                # 如果CTR一致性显著改善，保存为最佳模型
                current_consistency = eval_metrics.get('eval_ctr_consistency', 0.0) if 'eval_metrics' in locals() else 0.0
                if current_consistency >= best_ctr_consistency:
                    best_path = os.path.join(args.output_dir, 'improved_best_model.pt')
                    torch.save({
                        'epoch': epoch + 1,
                        'generator_state_dict': generator.state_dict(),
                        'discriminator_state_dict': discriminator.state_dict(),
                        'config': config,
                        'metrics': epoch_metrics
                    }, best_path)
                    logger.info(f"保存最佳模型，CTR一致性: {current_consistency:.3f}")
        
        logger.info("改进的训练成功完成！")
        logger.info(f"模型保存到: {args.output_dir}")
        logger.info(f"最佳CTR一致性: {best_ctr_consistency:.3f}")
        
        # 记录最终结果到wandb
        wandb.log({
            "training_completed": True,
            "final_epoch": config.epochs,
            "best_ctr_consistency": best_ctr_consistency,
            "final_d_loss": epoch_metrics['d_loss'],
            "final_g_loss": epoch_metrics['g_loss'],
            "ctr_consistency_threshold_met": best_ctr_consistency > 0.8
        })
        
    except Exception as e:
        logger.error(f"改进训练失败: {e}")
        logger.error(f"错误详情:", exc_info=True)
        raise
    
    finally:
        wandb.finish()


if __name__ == "__main__":
    main()
