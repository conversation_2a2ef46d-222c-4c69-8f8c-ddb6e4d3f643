#!/usr/bin/env python3
"""
使用改进的GAN生成高质量合成数据
主要改进：
1. 使用条件生成器，正确的CTR标签生成
2. 保持真实CTR分布
3. 更好的特征质量控制
4. 改进的数据后处理
"""

import os
import sys
import logging
import argparse
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

# Import improved modules
from src.gan.models import ImprovedConditionalGenerator, ImprovedDiscriminator
from src.gan.improved_data_prep import ImprovedGANDataProcessor
from src.gan.improved_trainer import ImprovedGANConfig
from src.data_process.utils import seed_everything


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Generate Improved Synthetic Data')
    
    # 模型相关
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--preprocessing_path', type=str, required=True,
                       help='Path to preprocessing info')
    
    # 生成参数
    parser.add_argument('--num_samples', type=int, default=100000,
                       help='Number of synthetic samples to generate')
    parser.add_argument('--batch_size', type=int, default=1000,
                       help='Batch size for generation')
    parser.add_argument('--target_ctr_rate', type=float, default=None,
                       help='Target CTR rate (if None, use balanced 50/50)')
    
    # 输出
    parser.add_argument('--output_path', type=str, required=True,
                       help='Output path for synthetic data')
    parser.add_argument('--save_raw', action='store_true',
                       help='Also save raw (non-reversed) synthetic data')
    
    # 质量控制
    parser.add_argument('--temperature', type=float, default=0.5,
                       help='Temperature for generation (lower = more deterministic)')
    parser.add_argument('--quality_check', action='store_true',
                       help='Perform quality checks on generated data')
    
    # 其他
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='Device to use for generation')
    
    return parser.parse_args()


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def load_model_and_processor(model_path: str, preprocessing_path: str, device: torch.device, logger):
    """加载模型和预处理器"""
    logger.info(f"Loading model from {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']
    
    # 加载预处理信息
    logger.info(f"Loading preprocessing info from {preprocessing_path}")

    # 从preprocessing_path目录中加载
    preprocessing_dir = os.path.dirname(preprocessing_path)
    processor = ImprovedGANDataProcessor('', '', preprocessing_dir)  # 设置正确的输出目录
    feature_info = processor.load_preprocessing_info(preprocessing_path)
    
    # 重建模型
    generator = ImprovedConditionalGenerator(
        noise_dim=config.noise_dim,
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim,
        ctr_embedding_dim=config.ctr_embedding_dim
    )
    
    discriminator = ImprovedDiscriminator(
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim,
        hidden_dim=config.discriminator_hidden_dim
    )
    
    # 加载权重
    generator.load_state_dict(checkpoint['generator_state_dict'])
    discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
    
    # 移动到设备
    generator.to(device)
    discriminator.to(device)
    
    # 设置为评估模式
    generator.eval()
    discriminator.eval()
    
    logger.info("Model and processor loaded successfully")
    return generator, discriminator, processor, config


def generate_ctr_labels(num_samples: int, target_ctr_rate: float, device: torch.device) -> torch.Tensor:
    """
    生成CTR标签 - 改进版本
    """
    if target_ctr_rate is None:
        # 平衡生成：50% positive, 50% negative
        num_positive = num_samples // 2
        num_negative = num_samples - num_positive
        ctr_labels = torch.cat([
            torch.ones(num_positive, device=device, dtype=torch.long),
            torch.zeros(num_negative, device=device, dtype=torch.long)
        ])
        # 随机打乱
        perm = torch.randperm(num_samples, device=device)
        ctr_labels = ctr_labels[perm]
    else:
        # 按指定CTR率生成
        ctr_labels = torch.bernoulli(
            torch.full((num_samples,), target_ctr_rate, device=device)
        ).long()
    
    return ctr_labels


def generate_batch(generator: ImprovedConditionalGenerator, 
                  batch_size: int, noise_dim: int, 
                  target_ctr_rate: float, temperature: float,
                  device: torch.device) -> dict:
    """生成一个批次的合成数据"""
    
    # 生成噪声
    noise = torch.randn(batch_size, noise_dim, device=device)
    
    # 生成CTR标签
    ctr_labels = generate_ctr_labels(batch_size, target_ctr_rate, device)
    
    # 生成数据
    with torch.no_grad():
        gen_output = generator(
            noise=noise,
            ctr_labels=ctr_labels,
            temperature=temperature,
            hard=True
        )
    
    # 转换为numpy
    batch_data = {}
    
    # 数值特征
    if gen_output['numeric'] is not None:
        batch_data['numeric'] = gen_output['numeric'].cpu().numpy()
    
    # 类别特征
    if gen_output['categorical_probs'] is not None:
        cat_indices = []
        for probs in gen_output['categorical_probs']:
            if probs is not None:
                indices = torch.argmax(probs, dim=-1).cpu().numpy()
                cat_indices.append(indices)
        if cat_indices:
            batch_data['categorical'] = np.column_stack(cat_indices)
    
    # CTR标签
    batch_data['ctr_labels'] = ctr_labels.cpu().numpy()
    
    return batch_data


def generate_synthetic_data(generator: ImprovedConditionalGenerator,
                          processor: ImprovedGANDataProcessor,
                          config: ImprovedGANConfig,
                          num_samples: int, batch_size: int,
                          target_ctr_rate: float, temperature: float,
                          device: torch.device, logger) -> pd.DataFrame:
    """生成完整的合成数据集"""
    
    logger.info(f"Generating {num_samples} synthetic samples")
    logger.info(f"Target CTR rate: {target_ctr_rate if target_ctr_rate else 'Balanced (50/50)'}")
    logger.info(f"Temperature: {temperature}")
    
    all_batches = []
    num_batches = (num_samples + batch_size - 1) // batch_size
    
    for i in range(num_batches):
        current_batch_size = min(batch_size, num_samples - i * batch_size)
        
        batch_data = generate_batch(
            generator, current_batch_size, config.noise_dim,
            target_ctr_rate, temperature, device
        )
        
        all_batches.append(batch_data)
        
        if (i + 1) % 10 == 0:
            logger.info(f"Generated {(i + 1) * batch_size} / {num_samples} samples")
    
    # 合并所有批次
    combined_data = {}
    
    if all_batches[0].get('numeric') is not None:
        combined_data['numeric'] = np.vstack([b['numeric'] for b in all_batches])
    
    if all_batches[0].get('categorical') is not None:
        combined_data['categorical'] = np.vstack([b['categorical'] for b in all_batches])
    
    combined_data['ctr_labels'] = np.concatenate([b['ctr_labels'] for b in all_batches])
    
    # 转换为DataFrame
    df_data = {}
    
    # 数值特征
    if 'numeric' in combined_data:
        for i, col in enumerate(processor.numeric_features):
            df_data[col] = combined_data['numeric'][:, i]
    
    # 类别特征
    if 'categorical' in combined_data:
        for i, col in enumerate(processor.categorical_features):
            df_data[f"{col}_idx"] = combined_data['categorical'][:, i]
    
    # CTR标签
    df_data[processor.label_col] = combined_data['ctr_labels']
    
    synthetic_df = pd.DataFrame(df_data)
    
    logger.info(f"Generated synthetic data shape: {synthetic_df.shape}")
    logger.info(f"Actual CTR rate: {combined_data['ctr_labels'].mean():.4f}")
    
    return synthetic_df


def quality_check(synthetic_df: pd.DataFrame, processor: ImprovedGANDataProcessor, logger):
    """对生成的数据进行质量检查"""
    logger.info("Performing quality checks on synthetic data")
    
    # 检查数值特征
    for col in processor.numeric_features:
        if col in synthetic_df.columns:
            std_val = synthetic_df[col].std()
            mean_val = synthetic_df[col].mean()
            logger.info(f"Numeric feature {col}: mean={mean_val:.4f}, std={std_val:.4f}")
            
            if std_val < 0.01:
                logger.warning(f"Low variance in {col}: {std_val:.6f}")
    
    # 检查类别特征
    for col in processor.categorical_features:
        idx_col = f"{col}_idx"
        if idx_col in synthetic_df.columns:
            unique_count = synthetic_df[idx_col].nunique()
            logger.info(f"Categorical feature {col}: {unique_count} unique values")
            
            if unique_count < 5:
                logger.warning(f"Low diversity in {col}: {unique_count} unique values")
    
    # 检查CTR分布
    ctr_rate = synthetic_df[processor.label_col].mean()
    logger.info(f"CTR rate: {ctr_rate:.4f}")
    
    # 检查缺失值
    missing_counts = synthetic_df.isnull().sum()
    if missing_counts.sum() > 0:
        logger.warning(f"Missing values found: {missing_counts[missing_counts > 0].to_dict()}")
    else:
        logger.info("No missing values found")


def main():
    """主函数"""
    args = parse_args()
    logger = setup_logging()
    
    # 设置种子
    seed_everything(args.seed)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    
    try:
        # 加载模型和预处理器
        generator, discriminator, processor, config = load_model_and_processor(
            args.model_path, args.preprocessing_path, device, logger
        )
        
        # 生成合成数据
        synthetic_df = generate_synthetic_data(
            generator, processor, config,
            args.num_samples, args.batch_size,
            args.target_ctr_rate, args.temperature,
            device, logger
        )
        
        # 质量检查
        if args.quality_check:
            quality_check(synthetic_df, processor, logger)
        
        # 保存原始数据（如果需要）
        if args.save_raw:
            raw_path = args.output_path.replace('.csv', '_raw.csv')
            synthetic_df.to_csv(raw_path, index=False)
            logger.info(f"Raw synthetic data saved to {raw_path}")
        
        # 反预处理到原始格式
        logger.info("Converting to original format...")
        synthetic_df_original = processor.reverse_preprocessing(synthetic_df)
        
        # 保存最终数据
        synthetic_df_original.to_csv(args.output_path, index=False)
        logger.info(f"Synthetic data saved to {args.output_path}")
        
        # 最终统计
        logger.info(f"Final synthetic data statistics:")
        logger.info(f"  Shape: {synthetic_df_original.shape}")
        logger.info(f"  CTR rate: {synthetic_df_original[processor.label_col].mean():.4f}")
        
        # 数值特征统计
        for col in processor.numeric_features:
            if col in synthetic_df_original.columns:
                logger.info(f"  {col}: mean={synthetic_df_original[col].mean():.4f}, std={synthetic_df_original[col].std():.4f}")
        
        logger.info("Synthetic data generation completed successfully!")
        
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        logger.error(f"Error details:", exc_info=True)
        raise


if __name__ == "__main__":
    main()
